// src/pages/StatsPage.tsx

import React, { useEffect, useState, useMemo } from 'react';
import styled, { DefaultTheme, css } from 'styled-components';
import { Link } from 'react-router-dom';
import { fetchSessionManifest } from '../data/sessions';
import { SessionManifestEntry } from '../models';
import { useLang, Language } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON>oader, FiBarChart2, FiBookOpen, FiClock, FiHeart, FiAward, FiTrendingUp, FiCalendar, FiPieChart, FiList
} from 'react-icons/fi';

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 900px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    svg {
        opacity: 0.9;
    }
  }
  p {
    font-size: 1.05rem;
    color: ${({ theme }) => theme.textSecondary};
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); /* Plus petites cartes pour potentiellement 3-4 par ligne */
  gap: 1.5rem;
  margin-bottom: 3rem;
`;

const StatCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px; /* Plus arrondi */
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.8rem 1.5rem; /* Plus de padding vertical */
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
  }

  .icon-container {
    font-size: 2.2rem; /* Légèrement plus petit si les cartes sont plus petites */
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
    padding: 0.9rem; /* Ajusté */
    background-color: ${({ theme }) => theme.primary}2A; /* Opacité un peu plus forte */
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px; /* Taille fixe pour l'icône */
    height: 60px;
  }

  h2 {
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-top: 0;
    margin-bottom: 0.4rem;
    font-weight: 600; /* Plus gras */
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  .stat-value {
    font-size: 2rem;
    color: ${({ theme }) => theme.primary};
    font-weight: 700;
    margin: 0 0 0.3rem 0; /* Espace sous la valeur */
  }
  .small-text {
    font-size: 0.8rem;
    color: ${({ theme }) => theme.textMuted};
    margin-top: 0.3rem;
    line-height: 1.3;
  }
`;

const DetailedStatsSection = styled.div`
  margin-bottom: 2.5rem; /* Espace entre les sections de détail */
  background: ${({ theme }) => theme.surface}; /* Surface pour se démarquer du fond général */
  padding: 1.8rem; /* Plus de padding */
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};

  h3 {
    font-size: 1.4rem; /* Plus grand */
    color: ${({ theme }) => theme.primary};
    margin-top: 0;
    margin-bottom: 1.2rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid ${({ theme }) => theme.border};
    display: flex;
    align-items: center;
    gap: 0.6rem;
  }
`;

const StatsList = styled.ul`
  list-style: none;
  padding: 0;
  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.9rem 0.5rem; /* Padding ajusté */
    border-bottom: 1px solid ${({ theme }) => theme.border}99; /* Bordure plus visible */
    font-size: 1rem; /* Taille de police de base */
    color: ${({ theme }) => theme.textSecondary};
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }
    &:hover {
      background-color: ${({theme}) => theme.surfaceAlt};
    }

    a { /* Style pour le lien dans la liste */
        color: ${({ theme }) => theme.text}; /* Couleur de texte principale pour le lien */
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
        &:hover {
            color: ${({ theme }) => theme.primary};
        }
    }

    strong { /* Pour le nom de la session/type */
      flex-grow: 1;
      margin-right: 1rem; /* Espace avant la valeur */
    }
    span { /* Pour le compteur/durée */
      font-weight: 500;
      color: ${({ theme }) => theme.primary};
      white-space: nowrap;
    }
  }
`;

const InfoText = styled.p`
  font-size: 1rem; /* Augmenté */
  color: ${({ theme }) => theme.textMuted};
  text-align: center;
  padding: 1.5rem 0; /* Plus de padding */
  line-height: 1.6;
  font-style: italic;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.primary};
  min-height: 70vh; /* Occupe plus de place */
  svg { font-size: 3.5rem; margin-bottom: 1.5rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'}33;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 50vh;
  justify-content: center;

  p { margin-bottom: 1rem; }
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: underline;
    font-weight: 500;
  }
`;

const formatDuration = (totalMinutes: number, t: any): string => {
  if (totalMinutes === 0) return `0 ${t('stats.duration.min', 'min')}`;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  let result = '';
  if (hours > 0) result += `${hours}${t('stats.duration.hours', 'h')} `;
  if (minutes > 0 || hours === 0) result += `${minutes}${t('stats.duration.min', 'min')}`;
  return result.trim();
};

const getAllJournalEntries = (): Record<string, string[]> => {
  const raw = localStorage.getItem('piknowkyo_journal');
  try {
    if (raw) {
      const parsed = JSON.parse(raw);
      if (typeof parsed === 'object' && parsed !== null) {
        for (const key in parsed) {
          if (!Array.isArray(parsed[key]) || !parsed[key].every((item: any) => typeof item === 'string')) {
            delete parsed[key];
          }
        }
        return parsed;
      }
    }
  } catch (error) { console.error("Erreur de parsing du journal:", error); }
  return {};
};

const StatsPage: React.FC = () => {
  const { t } = useTranslation();
  const { lang } = useLang();

  const [allSessionMetadata, setAllSessionMetadata] = useState<SessionManifestEntry[]>([]);
  const [journalEntries, setJournalEntries] = useState<Record<string, string[]>>(getAllJournalEntries());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const manifestData = await fetchSessionManifest(lang);
        setAllSessionMetadata(manifestData);
      } catch (err) {
        console.error("Error loading session manifest for stats:", err);
        setError(err instanceof Error ? err.message : t('errors.cantLoadSessions', "Impossible de charger les données des sessions."));
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, [lang, t]);

  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'piknowkyo_journal' || event.key === null) {
        setJournalEntries(getAllJournalEntries());
      }
    };
    setJournalEntries(getAllJournalEntries());
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const stats = useMemo(() => {
    const sessionIdsWithNotes = Object.keys(journalEntries);
    const totalSessionsFollowed = sessionIdsWithNotes.length;
    const totalNotesWritten = Object.values(journalEntries).reduce((acc, arr) => acc + (arr?.length || 0), 0);

    let favoriteSessionTitle: string | undefined = undefined;
    let favoriteSessionId: string | undefined = undefined;
    let totalTimeSpent = 0;
    const sessionsTimeSpent: { [title: string]: { count: number; totalDuration: number, id: string } } = {};
    const sessionTypesFollowed: { [type: string]: { count: number, totalDuration: number } } = {}; // Ajout totalDuration ici aussi

    if (allSessionMetadata.length > 0 && totalSessionsFollowed > 0) {
      const sortedJournalEntries = Object.entries(journalEntries).sort((a, b) => (b[1]?.length || 0) - (a[1]?.length || 0));
      if (sortedJournalEntries.length > 0) {
        const favEntry = sortedJournalEntries[0];
        favoriteSessionId = favEntry[0];
        const favSessionMeta = allSessionMetadata.find(s => s.id === favoriteSessionId);
        favoriteSessionTitle = favSessionMeta?.title;
      }

      sessionIdsWithNotes.forEach(id => {
        const sessionMeta = allSessionMetadata.find(s => s.id === id);
        if (sessionMeta) {
          const duration = sessionMeta.estimatedDuration || 0;
          const notesCountForThisSession = journalEntries[id]?.length || 0;

          if (notesCountForThisSession > 0) {
            const timeForThisSessionType = duration * notesCountForThisSession;
            totalTimeSpent += timeForThisSessionType;

            if (sessionsTimeSpent[sessionMeta.title]) {
              sessionsTimeSpent[sessionMeta.title].count += notesCountForThisSession;
              sessionsTimeSpent[sessionMeta.title].totalDuration += timeForThisSessionType;
            } else {
              sessionsTimeSpent[sessionMeta.title] = {
                  count: notesCountForThisSession,
                  totalDuration: timeForThisSessionType,
                  id: sessionMeta.id
              };
            }
            if (sessionTypesFollowed[sessionMeta.type]) {
                sessionTypesFollowed[sessionMeta.type].count += notesCountForThisSession;
                sessionTypesFollowed[sessionMeta.type].totalDuration += timeForThisSessionType;
            } else {
                sessionTypesFollowed[sessionMeta.type] = { count: notesCountForThisSession, totalDuration: timeForThisSessionType };
            }
          }
        }
      });
    }

    return {
      totalSessionsFollowed,
      totalNotesWritten,
      favoriteSessionTitle,
      favoriteSessionId,
      totalTimeSpent,
      sessionsTimeSpent,
      sessionTypesFollowed,
    };
  }, [allSessionMetadata, journalEntries]);

  if (isLoading) {
    return <LoadingContainer><FiLoader /> {t('loading.stats', 'Chargement des statistiques...')}</LoadingContainer>;
  }

  if (error) {
    return <ErrorMessage><p>{error}</p><Link to="/">{t('actions.backToHome', "Retour à l'accueil")}</Link></ErrorMessage>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiBarChart2 /> {t('stats.title', 'Vos Statistiques de Bien-être')}</h1>
        <p>{t('stats.description', 'Suivez votre parcours, célébrez vos progrès et découvrez vos tendances.')}</p>
      </PageHeader>

      <StatsGrid>
        <StatCard>
          <div className="icon-container"><FiBookOpen /></div>
          <h2>{t('stats.sessionsFollowed', 'Séances Pratiquées')}</h2>
          <p className="stat-value">{stats.totalSessionsFollowed}</p>
          <div className="small-text">{t('stats.sessionsFollowedDesc', 'Nombre de sessions uniques avec des notes.')}</div>
        </StatCard>
        <StatCard>
          <div className="icon-container"><FiClock /></div>
          <h2>{t('stats.totalTime', 'Temps Total en Séance')}</h2>
          <p className="stat-value">{formatDuration(stats.totalTimeSpent, t)}</p>
          <div className="small-text">{t('stats.totalTimeDesc', 'Temps cumulé estimé.')}</div>
        </StatCard>
        {stats.favoriteSessionTitle && stats.favoriteSessionId && (
          <StatCard as={Link} to={`/sessions/${stats.favoriteSessionId}`} style={{textDecoration: 'none', color: 'inherit'}}>
            <div className="icon-container"><FiHeart /></div>
            <h2>{t('stats.favoriteSession', 'Séance Favorite')}</h2>
            <p className="stat-value" style={{fontSize: '1.5rem', lineHeight: '1.3'}}>{stats.favoriteSessionTitle}</p>
            <div className="small-text">{t('stats.favoriteSessionDesc', 'La plus notée.')}</div>
          </StatCard>
        )}
         <StatCard>
          <div className="icon-container"><FiAward /></div>
          <h2>{t('stats.notesWritten', 'Total Notes Écrites')}</h2>
          <p className="stat-value">{stats.totalNotesWritten}</p>
          <div className="small-text">{t('stats.notesWrittenDesc', 'Nombre de réflexions enregistrées.')}</div>
        </StatCard>
      </StatsGrid>

      <DetailedStatsSection>
        <h3><FiPieChart /> {t('stats.typesFollowed', 'Répartition par Type de Séance')}</h3>
        {Object.keys(stats.sessionTypesFollowed).length > 0 ? (
          <StatsList>
            {Object.entries(stats.sessionTypesFollowed)
              .sort(([, a], [, b]) => b.count - a.count)
              .map(([type, data]) => (
              <li key={type}>
                <strong>{type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                <span>{data.count} {data.count > 1 ? t('stats.timesPlural', 'fois') : t('stats.timesSingular', 'fois')} ({formatDuration(data.totalDuration, t)})</span>
              </li>
            ))}
          </StatsList>
        ) : (
          <InfoText>{t('stats.noTypesYet', 'Aucun type de séance spécifique suivi pour le moment.')}</InfoText>
        )}
      </DetailedStatsSection>

      <DetailedStatsSection>
        <h3><FiList /> {t('stats.timePerSession', 'Détail par Séance (Estimé)')}</h3>
        {Object.keys(stats.sessionsTimeSpent).length > 0 ? (
        <StatsList>
          {Object.entries(stats.sessionsTimeSpent)
            .sort(([, a], [, b]) => b.totalDuration - a.totalDuration)
            .map(([title, data]) => (
            <li key={data.id}>
              <Link to={`/sessions/${data.id}`}>
                {title}
              </Link>
              <span>
                {formatDuration(data.totalDuration, t)} ({data.count} {data.count > 1 ? t('stats.notesPlural', 'notes') : t('stats.noteSingular', 'note')})
              </span>
            </li>
          ))}
        </StatsList>
        ) : (
            <InfoText>{t('stats.noTimePerSession', 'Aucune donnée de temps par séance disponible.')}</InfoText>
        )}
      </DetailedStatsSection>
    </PageContainer>
  );
};

export default StatsPage;