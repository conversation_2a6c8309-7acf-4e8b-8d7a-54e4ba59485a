import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { useAccessControl } from '../hooks/useAccessControl';
import { scriptSyncService } from '../services/scriptSyncService';

const TestContainer = styled.div`
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
`;

const Section = styled.div`
  margin-bottom: 2rem;
  padding: 1rem;
  border: 1px solid ${({ theme }) => theme.border || '#e1e5e9'};
  border-radius: 8px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 1rem 0;
  color: ${({ theme }) => theme.primary};
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const Label = styled.span`
  font-size: 0.875rem;
  font-weight: 600;
  color: ${({ theme }) => theme.textSecondary || '#6b7280'};
`;

const Value = styled.span`
  font-size: 1rem;
  color: ${({ theme }) => theme.text};
`;

const Button = styled.button`
  padding: 0.75rem 1rem;
  background: ${({ theme }) => theme.primary};
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;

  &:hover {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const StatusBadge = styled.span<{ $status: 'success' | 'error' | 'warning' }>`
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  background: ${({ $status }) => {
    switch ($status) {
      case 'success': return '#10b981';
      case 'error': return '#ef4444';
      case 'warning': return '#f59e0b';
      default: return '#6b7280';
    }
  }};
  color: white;
`;

const LogContainer = styled.div`
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
  font-family: monospace;
  font-size: 0.875rem;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
`;

const AuthTest: React.FC = () => {
  const { 
    user, 
    userProfile, 
    isAuthenticated, 
    loading,
    hasActivePremium,
    getRemainingTrialDays,
    signInAsGuest,
    signOut
  } = useAuth();

  const {
    canAccessPremiumSessions,
    canAccessBinauralAudio,
    canAccessGames,
    hasNoAds,
    featureAccess
  } = useAccessControl();

  const [testLogs, setTestLogs] = useState<string[]>([]);
  const [testing, setTesting] = useState(false);

  const addLog = (message: string) => {
    setTestLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  const testFirebaseConnection = async () => {
    setTesting(true);
    addLog('🔥 Test de connexion Firebase...');
    
    try {
      // Test de chargement du manifeste depuis Firestore
      const manifest = await scriptSyncService.getManifestFromFirestore('fr');
      addLog(`✅ Manifeste chargé: ${manifest.length} scripts trouvés`);
      
      // Test de chargement d'un script spécifique
      if (manifest.length > 0) {
        const firstScript = await scriptSyncService.getScriptFromFirestore(manifest[0].id, 'fr');
        addLog(`✅ Script chargé: ${firstScript ? firstScript.title : 'Aucun script trouvé'}`);
      }
      
      addLog('🎉 Tests Firebase réussis !');
    } catch (error) {
      addLog(`❌ Erreur Firebase: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  const testUploadScripts = async () => {
    setTesting(true);
    addLog('📤 Test d\'upload des scripts...');
    
    try {
      await scriptSyncService.uploadAllLocalScriptsToFirestore();
      addLog('✅ Upload des scripts terminé !');
    } catch (error) {
      addLog(`❌ Erreur d'upload: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  const handleGuestSignIn = async () => {
    try {
      addLog('👤 Connexion en tant qu\'invité...');
      await signInAsGuest();
      addLog('✅ Connexion réussie !');
    } catch (error) {
      addLog(`❌ Erreur de connexion: ${error}`);
    }
  };

  const handleSignOut = async () => {
    try {
      addLog('🚪 Déconnexion...');
      await signOut();
      addLog('✅ Déconnexion réussie !');
    } catch (error) {
      addLog(`❌ Erreur de déconnexion: ${error}`);
    }
  };

  return (
    <TestContainer>
      <h2>🧪 Test d'Authentification Firebase</h2>
      
      {/* État d'authentification */}
      <Section>
        <SectionTitle>État d'Authentification</SectionTitle>
        <InfoGrid>
          <InfoItem>
            <Label>Statut</Label>
            <Value>
              {loading ? (
                <StatusBadge $status="warning">Chargement...</StatusBadge>
              ) : isAuthenticated ? (
                <StatusBadge $status="success">Connecté</StatusBadge>
              ) : (
                <StatusBadge $status="error">Non connecté</StatusBadge>
              )}
            </Value>
          </InfoItem>
          
          <InfoItem>
            <Label>Utilisateur</Label>
            <Value>{user?.email || user?.uid || 'Aucun'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>Type de compte</Label>
            <Value>{user?.isAnonymous ? 'Invité' : 'Compte'}</Value>
          </InfoItem>
          
          <InfoItem>
            <Label>Plan d'abonnement</Label>
            <Value>{userProfile?.subscription.plan || 'N/A'}</Value>
          </InfoItem>
        </InfoGrid>
        
        <div>
          <Button onClick={handleGuestSignIn} disabled={isAuthenticated || loading}>
            Connexion Invité
          </Button>
          <Button onClick={handleSignOut} disabled={!isAuthenticated || loading}>
            Déconnexion
          </Button>
        </div>
      </Section>

      {/* Informations d'abonnement */}
      {userProfile && (
        <Section>
          <SectionTitle>Informations d'Abonnement</SectionTitle>
          <InfoGrid>
            <InfoItem>
              <Label>Plan</Label>
              <Value>{userProfile.subscription.plan}</Value>
            </InfoItem>
            
            <InfoItem>
              <Label>Statut</Label>
              <Value>{userProfile.subscription.status}</Value>
            </InfoItem>
            
            <InfoItem>
              <Label>Essai actif</Label>
              <Value>{userProfile.subscription.isTrialActive ? 'Oui' : 'Non'}</Value>
            </InfoItem>
            
            <InfoItem>
              <Label>Jours d'essai restants</Label>
              <Value>{getRemainingTrialDays()}</Value>
            </InfoItem>
          </InfoGrid>
        </Section>
      )}

      {/* Accès aux fonctionnalités */}
      <Section>
        <SectionTitle>Accès aux Fonctionnalités</SectionTitle>
        <InfoGrid>
          <InfoItem>
            <Label>Sessions Premium</Label>
            <Value>
              <StatusBadge $status={canAccessPremiumSessions ? 'success' : 'error'}>
                {canAccessPremiumSessions ? 'Autorisé' : 'Bloqué'}
              </StatusBadge>
            </Value>
          </InfoItem>
          
          <InfoItem>
            <Label>Audio Binaural</Label>
            <Value>
              <StatusBadge $status={canAccessBinauralAudio ? 'success' : 'error'}>
                {canAccessBinauralAudio ? 'Autorisé' : 'Bloqué'}
              </StatusBadge>
            </Value>
          </InfoItem>
          
          <InfoItem>
            <Label>Jeux</Label>
            <Value>
              <StatusBadge $status={canAccessGames ? 'success' : 'error'}>
                {canAccessGames ? 'Autorisé' : 'Bloqué'}
              </StatusBadge>
            </Value>
          </InfoItem>
          
          <InfoItem>
            <Label>Sans Publicité</Label>
            <Value>
              <StatusBadge $status={hasNoAds ? 'success' : 'error'}>
                {hasNoAds ? 'Oui' : 'Non'}
              </StatusBadge>
            </Value>
          </InfoItem>
        </InfoGrid>
      </Section>

      {/* Tests Firebase */}
      <Section>
        <SectionTitle>Tests Firebase</SectionTitle>
        <div>
          <Button onClick={testFirebaseConnection} disabled={testing}>
            Tester Connexion Firebase
          </Button>
          <Button onClick={testUploadScripts} disabled={testing}>
            Uploader Scripts
          </Button>
        </div>
        
        {testLogs.length > 0 && (
          <LogContainer>
            {testLogs.join('\n')}
          </LogContainer>
        )}
      </Section>
    </TestContainer>
  );
};

export default AuthTest;
