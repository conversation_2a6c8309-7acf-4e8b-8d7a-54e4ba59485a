import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import SplashScreen from './SplashScreen';
import SecureAuthModal from './SecureAuthModal';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * Composant pour protéger les routes - empêche l'accès sans authentification
 * Contrairement au modal d'authentification, ce composant ne peut pas être contourné
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  // Afficher le splash screen pendant le chargement de l'authentification
  if (loading) {
    return <SplashScreen />;
  }

  // Si l'utilisateur n'est pas authentifié, afficher le modal d'authentification sécurisé
  // sans possibilité de le fermer
  if (!isAuthenticated) {
    return (
      <SecureAuthModal
        isOpen={true}
        initialMode="signup"
      />
    );
  }

  // Si l'utilisateur est authentifié, afficher le contenu protégé
  return <>{children}</>;
};

export default ProtectedRoute;
