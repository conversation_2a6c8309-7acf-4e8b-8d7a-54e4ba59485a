import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import SplashScreen from './SplashScreen';
import AuthModal from './AuthModal';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * Composant pour protéger les routes - empêche l'accès sans authentification
 * Contrairement au modal d'authentification, ce composant ne peut pas être contourné
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  // Afficher le splash screen pendant le chargement de l'authentification
  if (loading) {
    return <SplashScreen />;
  }

  // Si l'utilisateur n'est pas authentifié, afficher le modal d'authentification
  // sans possibilité de le fermer
  if (!isAuthenticated) {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        background: 'rgba(0, 0, 0, 0.8)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <AuthModal
          isOpen={true}
          onClose={() => {}} // Fonction vide - impossible de fermer le modal
          initialMode="signup"
        />
      </div>
    );
  }

  // Si l'utilisateur est authentifié, afficher le contenu protégé
  return <>{children}</>;
};

export default ProtectedRoute;
