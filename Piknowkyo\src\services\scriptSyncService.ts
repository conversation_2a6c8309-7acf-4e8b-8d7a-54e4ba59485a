import { 
  collection, 
  doc, 
  getDocs, 
  setDoc, 
  onSnapshot, 
  query, 
  where, 
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { db } from '../firebase';
import { Session, SessionManifestEntry } from '../models';
import { Language } from '../LangProvider';

export interface FirestoreScript {
  id: string;
  language: Language;
  title: string;
  description: string;
  type: string;
  estimatedDuration: number;
  tags: string[];
  isPremium: boolean;
  imageUrl?: string;
  script?: any[];
  audio?: any;
  benefits?: string[];
  comments?: any[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  version: number;
}

export class ScriptSyncService {
  private static instance: ScriptSyncService;
  private listeners: Map<string, () => void> = new Map();

  static getInstance(): ScriptSyncService {
    if (!ScriptSyncService.instance) {
      ScriptSyncService.instance = new ScriptSyncService();
    }
    return ScriptSyncService.instance;
  }

  /**
   * Synchroniser tous les scripts locaux vers Firestore
   * Cette fonction doit être exécutée une seule fois pour migrer les scripts existants
   */
  async uploadAllLocalScriptsToFirestore(): Promise<void> {
    console.log('🚀 Début de la synchronisation des scripts vers Firestore...');
    
    const languages: Language[] = ['fr', 'en', 'es'];
    let totalUploaded = 0;

    for (const lang of languages) {
      try {
        console.log(`📁 Traitement de la langue: ${lang}`);
        
        // Charger le manifeste pour cette langue
        const manifest = await this.loadLocalManifest(lang);
        if (!manifest || manifest.length === 0) {
          console.log(`⚠️ Aucun script trouvé pour la langue ${lang}`);
          continue;
        }

        console.log(`📋 ${manifest.length} scripts trouvés pour ${lang}`);

        // Pour chaque script dans le manifeste
        for (const manifestEntry of manifest) {
          try {
            // Charger le script complet depuis les fichiers locaux
            const fullScript = await this.loadLocalScript(manifestEntry.id, lang);
            if (!fullScript) {
              console.warn(`⚠️ Script ${manifestEntry.id} non trouvé pour ${lang}`);
              continue;
            }

            // Convertir et uploader vers Firestore
            await this.uploadScriptToFirestore(fullScript, lang);
            totalUploaded++;
            console.log(`✅ Script ${manifestEntry.id} (${lang}) uploadé`);

          } catch (error) {
            console.error(`❌ Erreur lors de l'upload du script ${manifestEntry.id} (${lang}):`, error);
          }
        }

      } catch (error) {
        console.error(`❌ Erreur lors du traitement de la langue ${lang}:`, error);
      }
    }

    console.log(`🎉 Synchronisation terminée ! ${totalUploaded} scripts uploadés au total.`);
  }

  /**
   * Charger le manifeste local pour une langue donnée
   */
  private async loadLocalManifest(lang: Language): Promise<SessionManifestEntry[]> {
    try {
      const manifestPath = `/assets/manifests/manifest_${lang}.json`;
      const response = await fetch(manifestPath);
      if (!response.ok) {
        throw new Error(`Manifeste non trouvé: ${manifestPath}`);
      }
      const manifestData = await response.json();
      return manifestData.sessions || [];
    } catch (error) {
      console.error(`Erreur lors du chargement du manifeste ${lang}:`, error);
      return [];
    }
  }

  /**
   * Charger un script local complet
   */
  private async loadLocalScript(scriptId: string, lang: Language): Promise<Session | null> {
    try {
      const scriptPath = `/assets/sessionScripts/${lang}/${scriptId}.json`;
      const response = await fetch(scriptPath);
      if (!response.ok) {
        return null;
      }
      return await response.json();
    } catch (error) {
      console.error(`Erreur lors du chargement du script ${scriptId} (${lang}):`, error);
      return null;
    }
  }

  /**
   * Uploader un script vers Firestore
   */
  private async uploadScriptToFirestore(script: Session, language: Language): Promise<void> {
    const firestoreScript: Omit<FirestoreScript, 'createdAt' | 'updatedAt'> = {
      id: script.id,
      language,
      title: script.title,
      description: script.description || '',
      type: script.type,
      estimatedDuration: script.estimatedDuration || 0,
      tags: script.tags || [],
      isPremium: script.isPremium || false,
      imageUrl: script.imageUrl,
      script: script.script,
      audio: script.audio,
      benefits: script.benefits,
      comments: script.comments,
      version: 1
    };

    const docRef = doc(db, 'scripts', `${script.id}_${language}`);
    await setDoc(docRef, {
      ...firestoreScript,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  }

  /**
   * Récupérer tous les scripts depuis Firestore pour une langue donnée
   */
  async getScriptsFromFirestore(language: Language): Promise<Session[]> {
    try {
      const scriptsRef = collection(db, 'scripts');
      const q = query(
        scriptsRef,
        where('language', '==', language),
        orderBy('title')
      );

      const querySnapshot = await getDocs(q);
      const scripts: Session[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data() as FirestoreScript;
        scripts.push(this.convertFirestoreToSession(data));
      });

      return scripts;
    } catch (error) {
      console.error(`Erreur lors de la récupération des scripts ${language}:`, error);
      return [];
    }
  }

  /**
   * Récupérer le manifeste depuis Firestore pour une langue donnée
   */
  async getManifestFromFirestore(language: Language): Promise<SessionManifestEntry[]> {
    try {
      const scriptsRef = collection(db, 'scripts');
      const q = query(
        scriptsRef,
        where('language', '==', language),
        orderBy('title')
      );

      const querySnapshot = await getDocs(q);
      const manifest: SessionManifestEntry[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data() as FirestoreScript;
        manifest.push({
          id: data.id,
          title: data.title,
          type: data.type,
          estimatedDuration: data.estimatedDuration,
          tags: data.tags,
          imageUrl: data.imageUrl,
          isPremium: data.isPremium
        });
      });

      return manifest;
    } catch (error) {
      console.error(`Erreur lors de la récupération du manifeste ${language}:`, error);
      return [];
    }
  }

  /**
   * Récupérer un script spécifique depuis Firestore
   */
  async getScriptFromFirestore(scriptId: string, language: Language): Promise<Session | null> {
    try {
      const docRef = doc(db, 'scripts', `${scriptId}_${language}`);
      const docSnap = await getDocs(query(collection(db, 'scripts'), where('id', '==', scriptId), where('language', '==', language)));
      
      if (!docSnap.empty) {
        const data = docSnap.docs[0].data() as FirestoreScript;
        return this.convertFirestoreToSession(data);
      }
      
      return null;
    } catch (error) {
      console.error(`Erreur lors de la récupération du script ${scriptId} (${language}):`, error);
      return null;
    }
  }

  /**
   * Écouter les changements en temps réel pour une langue donnée
   */
  subscribeToScripts(language: Language, callback: (scripts: Session[]) => void): () => void {
    const scriptsRef = collection(db, 'scripts');
    const q = query(
      scriptsRef,
      where('language', '==', language),
      orderBy('title')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const scripts: Session[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data() as FirestoreScript;
        scripts.push(this.convertFirestoreToSession(data));
      });
      callback(scripts);
    }, (error) => {
      console.error(`Erreur lors de l'écoute des scripts ${language}:`, error);
    });

    // Stocker le listener pour pouvoir le nettoyer plus tard
    const listenerId = `scripts_${language}`;
    this.listeners.set(listenerId, unsubscribe);

    return unsubscribe;
  }

  /**
   * Écouter les changements du manifeste en temps réel
   */
  subscribeToManifest(language: Language, callback: (manifest: SessionManifestEntry[]) => void): () => void {
    const scriptsRef = collection(db, 'scripts');
    const q = query(
      scriptsRef,
      where('language', '==', language),
      orderBy('title')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const manifest: SessionManifestEntry[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data() as FirestoreScript;
        manifest.push({
          id: data.id,
          title: data.title,
          type: data.type,
          estimatedDuration: data.estimatedDuration,
          tags: data.tags,
          imageUrl: data.imageUrl,
          isPremium: data.isPremium
        });
      });
      callback(manifest);
    }, (error) => {
      console.error(`Erreur lors de l'écoute du manifeste ${language}:`, error);
    });

    const listenerId = `manifest_${language}`;
    this.listeners.set(listenerId, unsubscribe);

    return unsubscribe;
  }

  /**
   * Convertir un document Firestore en Session
   */
  private convertFirestoreToSession(firestoreScript: FirestoreScript): Session {
    return {
      id: firestoreScript.id,
      title: firestoreScript.title,
      description: firestoreScript.description,
      type: firestoreScript.type,
      language: firestoreScript.language,
      estimatedDuration: firestoreScript.estimatedDuration,
      tags: firestoreScript.tags,
      isPremium: firestoreScript.isPremium,
      imageUrl: firestoreScript.imageUrl,
      script: firestoreScript.script,
      audio: firestoreScript.audio,
      benefits: firestoreScript.benefits,
      comments: firestoreScript.comments
    };
  }

  /**
   * Nettoyer tous les listeners
   */
  cleanup(): void {
    this.listeners.forEach((unsubscribe) => {
      unsubscribe();
    });
    this.listeners.clear();
  }
}

// Instance singleton
export const scriptSyncService = ScriptSyncService.getInstance();
