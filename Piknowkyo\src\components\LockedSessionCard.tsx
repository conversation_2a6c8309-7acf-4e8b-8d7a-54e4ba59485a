import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiLock, FiPlay, FiGift, FiStar } from 'react-icons/fi';
import { FaCrown } from 'react-icons/fa';
import { Session } from '../models';
import { useAccessControl } from '../hooks/useAccessControl';
import MonetizationModal from './MonetizationModal';

interface LockedSessionCardProps {
  session: Session;
  onUnlock?: (sessionId: string) => void;
}

const CardContainer = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  border: 1px solid ${({ theme }) => theme.border || '#e1e5e9'};
  overflow: hidden;
  transition: all 0.2s ease;
  opacity: 0.8;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    opacity: 1;
  }
`;

const ImageContainer = styled.div`
  position: relative;
  width: 100%;
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
`;

const SessionImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(2px) brightness(0.7);
`;

const LockOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(1px);
`;

const LockIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  color: #667eea;
  font-size: 1.5rem;
`;

const PremiumBadge = styled.div`
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 2;
`;

const CardContent = styled.div`
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const SessionTitle = styled.h3`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: ${({ theme }) => theme.text};
  line-height: 1.3;
`;

const SessionDescription = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.textSecondary || '#6b7280'};
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const SessionMeta = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: ${({ theme }) => theme.textSecondary || '#6b7280'};
`;

const Duration = styled.span`
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const Tags = styled.div`
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
`;

const Tag = styled.span`
  padding: 0.125rem 0.375rem;
  background: ${({ theme }) => theme.surfaceAlt || '#f3f4f6'};
  border-radius: 4px;
  font-size: 0.625rem;
`;

const UnlockActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

const UnlockButton = styled.button<{ $variant: 'primary' | 'secondary' }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  ${({ $variant }) => $variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  ` : `
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
    
    &:hover {
      background: #667eea;
      color: white;
    }
  `}

  &:active {
    transform: translateY(0);
  }
`;

const UpgradeMessage = styled.div`
  text-align: center;
  padding: 0.75rem;
  background: linear-gradient(135deg, #667eea10 0%, #764ba210 100%);
  border-radius: 8px;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.text};
  line-height: 1.4;
`;

const LockedSessionCard: React.FC<LockedSessionCardProps> = ({ session, onUnlock }) => {
  const { t } = useTranslation();
  const { canWatchAds, getUpgradeMessage } = useAccessControl();
  const [showMonetizationModal, setShowMonetizationModal] = useState(false);

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}min` : ''}`;
  };

  const handleUnlockClick = () => {
    setShowMonetizationModal(true);
  };

  const handleWatchAd = async () => {
    // Cette fonction sera appelée depuis MonetizationModal
    if (onUnlock) {
      onUnlock(session.id);
    }
    setShowMonetizationModal(false);
  };

  const handleUpgrade = () => {
    // Redirection vers la page de paiement
    console.log('Redirection vers la mise à niveau premium...');
    setShowMonetizationModal(false);
  };

  return (
    <>
      <CardContainer>
        <ImageContainer>
          {session.imageUrl ? (
            <SessionImage src={session.imageUrl} alt={session.title} />
          ) : (
            <div style={{ color: 'white', fontSize: '2rem' }}>🧘</div>
          )}
          
          <LockOverlay>
            <LockIcon>
              <FiLock />
            </LockIcon>
          </LockOverlay>

          <PremiumBadge>
            <FaCrown />
            {t('session.premium', 'Premium')}
          </PremiumBadge>
        </ImageContainer>

        <CardContent>
          <SessionTitle>{session.title}</SessionTitle>
          
          {session.description && (
            <SessionDescription>{session.description}</SessionDescription>
          )}

          <SessionMeta>
            <Duration>
              <FiPlay size={12} />
              {formatDuration(session.estimatedDuration || 0)}
            </Duration>
            
            {session.tags && session.tags.length > 0 && (
              <Tags>
                {session.tags.slice(0, 2).map((tag, index) => (
                  <Tag key={index}>{tag}</Tag>
                ))}
                {session.tags.length > 2 && (
                  <Tag>+{session.tags.length - 2}</Tag>
                )}
              </Tags>
            )}
          </SessionMeta>

          <UpgradeMessage>
            {getUpgradeMessage('cette session premium')}
          </UpgradeMessage>

          <UnlockActions>
            <UnlockButton $variant="primary" onClick={handleUnlockClick}>
              <FiStar />
              {t('session.unlock', 'Débloquer')}
            </UnlockButton>

            {canWatchAds && (
              <UnlockButton $variant="secondary" onClick={handleUnlockClick}>
                <FiGift />
                {t('session.watchAd', 'Regarder une pub')}
              </UnlockButton>
            )}
          </UnlockActions>
        </CardContent>
      </CardContainer>

      <MonetizationModal
        isOpen={showMonetizationModal}
        onClose={() => setShowMonetizationModal(false)}
        feature="session premium"
        sessionId={session.id}
        onWatchAd={handleWatchAd}
        onUpgrade={handleUpgrade}
      />
    </>
  );
};

export default LockedSessionCard;
