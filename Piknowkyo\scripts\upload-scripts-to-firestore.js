#!/usr/bin/env node

/**
 * Script pour uploader tous les scripts JSON vers Firestore
 * Usage: node scripts/upload-scripts-to-firestore.js
 */

const { initializeApp } = require('firebase/app');
const {
  getFirestore,
  collection,
  doc,
  setDoc,
  serverTimestamp,
  getDocs,
  query,
  where
} = require('firebase/firestore');
const fs = require('fs');
const path = require('path');

// Configuration Firebase
const firebaseConfig = {
  apiKey: "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: "piknowkyo-777.firebaseapp.com",
  projectId: "piknowkyo-777",
  storageBucket: "piknowkyo-777.firebasestorage.app",
  messagingSenderId: "375619599814",
  appId: "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

const LANGUAGES = ['fr', 'en', 'es'];

/**
 * Charger un fichier JSON
 */
function loadJsonFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️ Fichier non trouvé: ${filePath}`);
      return null;
    }
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ Erreur lors du chargement de ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Charger le manifeste pour une langue
 */
function loadManifest(language) {
  const manifestPath = path.join(__dirname, '..', 'public', 'assets', 'manifests', `manifest_${language}.json`);
  const manifest = loadJsonFile(manifestPath);
  return manifest?.sessions || [];
}

/**
 * Charger un script complet
 */
function loadScript(scriptId, language) {
  const scriptPath = path.join(__dirname, '..', 'public', 'assets', 'sessionScripts', language, `${scriptId}.json`);
  return loadJsonFile(scriptPath);
}

/**
 * Vérifier si un script existe déjà dans Firestore
 */
async function scriptExists(scriptId, language) {
  try {
    const scriptsRef = collection(db, 'scripts');
    const q = query(
      scriptsRef,
      where('id', '==', scriptId),
      where('language', '==', language)
    );
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error(`Erreur lors de la vérification du script ${scriptId} (${language}):`, error);
    return false;
  }
}

/**
 * Uploader un script vers Firestore
 */
async function uploadScript(script, language, forceUpdate = false) {
  try {
    const scriptId = script.id;
    const docId = `${scriptId}_${language}`;

    // Vérifier si le script existe déjà
    if (!forceUpdate && await scriptExists(scriptId, language)) {
      console.log(`⏭️ Script ${scriptId} (${language}) existe déjà, ignoré`);
      return false;
    }

    // Préparer les données pour Firestore
    const firestoreScript = {
      id: scriptId,
      language: language,
      title: script.title || 'Sans titre',
      description: script.description || '',
      type: script.type || 'custom',
      estimatedDuration: script.estimatedDuration || script.durationMinutes || 0,
      tags: Array.isArray(script.tags) ? script.tags : [],
      isPremium: Boolean(script.isPremium),
      imageUrl: script.imageUrl || null,
      script: script.script || [],
      audio: script.audio || null,
      benefits: Array.isArray(script.benefits) ? script.benefits : 
                (typeof script.benefits === 'string' ? script.benefits.split(',').map(b => b.trim()) : []),
      comments: script.comments || [],
      version: 1,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Uploader vers Firestore
    const docRef = doc(db, 'scripts', docId);
    await setDoc(docRef, firestoreScript);

    console.log(`✅ Script ${scriptId} (${language}) uploadé avec succès`);
    return true;
  } catch (error) {
    console.error(`❌ Erreur lors de l'upload du script ${script.id} (${language}):`, error);
    return false;
  }
}

/**
 * Traiter tous les scripts pour une langue
 */
async function processLanguage(language, forceUpdate = false) {
  console.log(`\n📁 Traitement de la langue: ${language.toUpperCase()}`);
  
  // Charger le manifeste
  const manifest = loadManifest(language);
  if (!manifest || manifest.length === 0) {
    console.log(`⚠️ Aucun script trouvé dans le manifeste pour ${language}`);
    return { processed: 0, uploaded: 0, errors: 0 };
  }

  console.log(`📋 ${manifest.length} scripts trouvés dans le manifeste`);

  let processed = 0;
  let uploaded = 0;
  let errors = 0;

  // Traiter chaque script
  for (const manifestEntry of manifest) {
    try {
      processed++;
      
      // Charger le script complet
      const fullScript = loadScript(manifestEntry.id, language);
      if (!fullScript) {
        console.warn(`⚠️ Script ${manifestEntry.id} non trouvé dans les fichiers`);
        errors++;
        continue;
      }

      // Fusionner les données du manifeste avec le script complet
      const mergedScript = {
        ...fullScript,
        id: manifestEntry.id,
        title: fullScript.title || manifestEntry.title,
        type: fullScript.type || manifestEntry.type,
        estimatedDuration: fullScript.estimatedDuration || manifestEntry.estimatedDuration,
        tags: fullScript.tags || manifestEntry.tags || [],
        isPremium: fullScript.isPremium !== undefined ? fullScript.isPremium : manifestEntry.isPremium,
        imageUrl: fullScript.imageUrl || manifestEntry.imageUrl
      };

      // Uploader vers Firestore
      const success = await uploadScript(mergedScript, language, forceUpdate);
      if (success) {
        uploaded++;
      }

    } catch (error) {
      console.error(`❌ Erreur lors du traitement de ${manifestEntry.id}:`, error);
      errors++;
    }
  }

  console.log(`📊 Langue ${language}: ${processed} traités, ${uploaded} uploadés, ${errors} erreurs`);
  return { processed, uploaded, errors };
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Début de l\'upload des scripts vers Firestore...\n');

  const forceUpdate = process.argv.includes('--force');
  if (forceUpdate) {
    console.log('🔄 Mode force activé - les scripts existants seront remplacés\n');
  }

  let totalProcessed = 0;
  let totalUploaded = 0;
  let totalErrors = 0;

  // Traiter chaque langue
  for (const language of LANGUAGES) {
    try {
      const result = await processLanguage(language, forceUpdate);
      totalProcessed += result.processed;
      totalUploaded += result.uploaded;
      totalErrors += result.errors;
    } catch (error) {
      console.error(`❌ Erreur lors du traitement de la langue ${language}:`, error);
      totalErrors++;
    }
  }

  // Résumé final
  console.log('\n🎉 Upload terminé !');
  console.log(`📊 Résumé global:`);
  console.log(`   - Scripts traités: ${totalProcessed}`);
  console.log(`   - Scripts uploadés: ${totalUploaded}`);
  console.log(`   - Erreurs: ${totalErrors}`);

  if (totalErrors > 0) {
    console.log('\n⚠️ Des erreurs se sont produites. Vérifiez les logs ci-dessus.');
    process.exit(1);
  } else {
    console.log('\n✅ Tous les scripts ont été traités avec succès !');
    process.exit(0);
  }
}

// Gestion des erreurs non capturées
process.on('unhandledRejection', (error) => {
  console.error('❌ Erreur non gérée:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
  process.exit(1);
});

// Lancer le script
main().catch((error) => {
  console.error('❌ Erreur fatale:', error);
  process.exit(1);
});
