rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Fonction pour vérifier si l'utilisateur est un admin
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/admin_users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.isActive == true;
    }
    
    // Fonction pour vérifier les permissions
    function hasPermission(permission) {
      return isAdmin() && 
             permission in get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.permissions;
    }
    
    // Fonction pour vérifier si l'utilisateur est super admin
    function isSuperAdmin() {
      return isAdmin() && 
             get(/databases/$(database)/documents/admin_users/$(request.auth.uid)).data.role == 'super_admin';
    }

    // Règles pour les utilisateurs administrateurs
    match /admin_users/{userId} {
      allow read: if isAdmin();
      allow write: if isSuperAdmin() || hasPermission('manage_acl');
    }

    // Règles pour les plans de tarification
    match /subscription_pricing/{planId} {
      allow read: if isAdmin();
      allow write: if hasPermission('manage_pricing');
    }

    // Règles pour les abonnements utilisateurs
    match /user_subscriptions/{subscriptionId} {
      allow read: if isAdmin();
      allow write: if hasPermission('manage_subscriptions');
    }

    // Règles pour les modèles de sessions
    match /session_templates/{sessionId} {
      allow read: if isAdmin();
      allow write: if hasPermission('manage_sessions') || hasPermission('manage_content');
    }

    // Règles pour les fournisseurs IA
    match /ai_providers/{providerId} {
      allow read: if isAdmin();
      allow write: if hasPermission('manage_ai_apis');
    }

    // Règles pour les utilisateurs de l'application principale
    match /users/{userId} {
      allow read: if isAdmin();
      allow write: if hasPermission('manage_users');
    }

    // Règles pour les sessions utilisateurs
    match /user_sessions/{sessionId} {
      allow read: if isAdmin() && hasPermission('view_analytics');
      allow write: if false; // Lecture seule pour les analytics
    }

    // Règles pour les journaux d'activité
    match /activity_logs/{logId} {
      allow read: if isAdmin() && hasPermission('view_analytics');
      allow create: if isAdmin(); // Les admins peuvent créer des logs
      allow update, delete: if false; // Les logs ne peuvent pas être modifiés
    }

    // Règles par défaut - refuser tout accès non autorisé
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
