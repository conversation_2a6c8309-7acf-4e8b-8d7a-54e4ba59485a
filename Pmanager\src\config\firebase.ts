import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator, initializeFirestore, persistentLocalCache, persistentMultipleTabManager } from 'firebase/firestore';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getStorage, connectStorageEmulator } from 'firebase/storage';

// Configuration Firebase - utilise la même que l'app principale
const firebaseConfig = {
  apiKey: "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: "piknowkyo-777.firebaseapp.com",
  projectId: "piknowkyo-777",
  storageBucket: "piknowkyo-777.firebasestorage.app",
  messagingSenderId: "375619599814",
  appId: "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);

// Initialiser Firestore avec la nouvelle API de cache
let db;
try {
  // Utiliser la nouvelle API de cache persistant
  db = initializeFirestore(app, {
    cache: persistentLocalCache({
      tabManager: persistentMultipleTabManager()
    })
  });
} catch (error) {
  // Fallback vers getFirestore si initializeFirestore échoue
  console.warn('Failed to initialize Firestore with persistent cache, using default:', error);
  db = getFirestore(app);
}

const auth = getAuth(app);
const storage = getStorage(app);

// Connecter aux émulateurs en développement
if (import.meta.env.DEV && import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true') {
  try {
    connectFirestoreEmulator(db, 'localhost', 8080);
    connectAuthEmulator(auth, 'http://localhost:9099');
    connectStorageEmulator(storage, 'localhost', 9199);
    console.log('Connected to Firebase emulators');
  } catch (error) {
    console.warn('Firebase emulators already connected or not available');
  }
}

export { db, auth, storage };
