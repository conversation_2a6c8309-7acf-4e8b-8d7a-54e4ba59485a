// src/services/ttsVoices.ts
import { TTSProvider } from './tts'; // Importer TTSProvider pour typer les clés

// Interface pour une voix TTS standard
export interface TTSVoice {
  id: string;        // Identifiant unique de la voix pour le provider
  label: string;     // Nom affiché à l'utilisateur
  lang: string;      // Code langue (ex: 'fr', 'en', 'es', ou plus spécifique comme 'fr-FR')
  gender?: 'male' | 'female'; // Optionnel
  disabled?: boolean;
  // Vous pouvez ajouter d'autres propriétés spécifiques si certains providers les fournissent
}

// Type pour l'objet ttsVoices, pour s'assurer que les clés sont des TTSProvider connus (ou string)
// et que les valeurs sont des tableaux de TTSVoice.
// Si vous avez des structures de voix différentes par provider,
// vous devrez utiliser une union de types plus complexe ou un type générique.
// Pour l'instant, on utilise TTSVoice pour tous pour la simplicité des filtres.


export type TTSVoicesCollection = {
  [key in TTSProvider | string]?: TTSVoice[]; // Permet des clés TTSProvider ou des chaînes pour flexibilité
};


// Ce fichier simule la liste des voix pour chaque fournisseur TTS, par langue.
// Dans une application réelle, ces listes seraient probablement récupérées via des appels API
// spécifiques à chaque fournisseur TTS, car elles peuvent changer.

// Pour simplifier, nous utilisons la même structure TTSVoice pour le moment.

export const ttsVoices: TTSVoicesCollection = {
  // Note: Pour 'browser', la liste réelle des voix est obtenue via window.speechSynthesis.getVoices().
  // Ce 'browser' ici est plus un placeholder si vous voulez le lister.
  // La logique dans SettingsPage.tsx pour le provider 'browser' récupère dynamiquement les voix.
  browser: [
    { id: 'auto', label: 'Automatique par langue (Navigateur)', lang: 'auto' },
    // Vous pouvez ajouter ici des voix spécifiques du navigateur si vous les connaissez à l'avance et voulez les afficher
    // { id: 'Microsoft Julie - French (France)', label: 'Julie (FR)', lang: 'fr-FR' }, // Exemple
  ],

  ttswebui: [
    { id: 'fr_female1_webui', label: 'WebUI FR - Femme 1', lang: 'fr' },
    { id: 'fr_male1_webui', label: 'WebUI FR - Homme 1', lang: 'fr' },
    { id: 'en_female1_webui', label: 'WebUI EN - Female 1', lang: 'en' },
    { id: 'es_male1_webui', label: 'WebUI ES - Hombre 1', lang: 'es' },
  ],
  openvoice: [
    { id: 'fr_openvoice_f1', label: 'OpenVoice FR - Femme 1', lang: 'fr' },
    { id: 'en_openvoice_f1', label: 'OpenVoice EN - Female 1', lang: 'en' },
    { id: 'es_openvoice_f1', label: 'OpenVoice ES - Mujer 1', lang: 'es' },
  ],
  parlertts: [
    { id: 'fr_parler_f1', label: 'Parler-TTS FR - Femme 1', lang: 'fr' },
    { id: 'en_parler_f1', label: 'Parler-TTS EN - Female 1', lang: 'en' },
    { id: 'es_parler_f1', label: 'Parler-TTS ES - Mujer 1', lang: 'es' },
  ],
  murf: [ // Ajout de Murf qui était manquant dans l'objet
    { id: 'murf_fr_celine', label: 'Murf FR - Celine', lang: 'fr', gender: 'female' },
    { id: 'murf_en_adam', label: 'Murf EN - Adam', lang: 'en', gender: 'male' },
    { id: 'murf_es_sofia', label: 'Murf ES - Sofia', lang: 'es', gender: 'female' },
  ],
  // Pour 'apicustom' et 'cloud', les voix seraient probablement configurées ailleurs
  // ou récupérées dynamiquement via l'API concernée.
  // Vous pouvez laisser ces clés vides ou les omettre si les voix sont toujours dynamiques.
  apicustom: [],
  cloud: [],
};

// Fonction utilitaire pour obtenir les voix, utilisée dans SettingsPage
// Elle est conservée car elle filtre par langue, ce qui est utile.
export const getVoicesForProviderAndLang = (providerId: TTSProvider | string, lang: string): TTSVoice[] => {
  const providerKey = providerId as keyof typeof ttsVoices; // Caster pour l'accès
  if (ttsVoices && providerKey in ttsVoices && Array.isArray(ttsVoices[providerKey])) {
    return (ttsVoices[providerKey] as TTSVoice[]).filter((v) => v.lang === lang || v.lang === 'auto');
  }
  return [];
};