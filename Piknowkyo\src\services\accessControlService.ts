import { UserProfile, SubscriptionPlan } from '../contexts/AuthContext';
import { Session } from '../models';

export interface AccessResult {
  allowed: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  trialAvailable?: boolean;
}

export interface FeatureAccess {
  sessions: {
    premium: boolean;
    unlimited: boolean;
    types: string[];
  };
  audio: {
    binaural: boolean;
    ambient: boolean;
    premium: boolean;
  };
  features: {
    noAds: boolean;
    offline: boolean;
    customization: boolean;
  };
}

export class AccessControlService {
  private static instance: AccessControlService;

  static getInstance(): AccessControlService {
    if (!AccessControlService.instance) {
      AccessControlService.instance = new AccessControlService();
    }
    return AccessControlService.instance;
  }

  /**
   * Vérifier l'accès à une session spécifique
   */
  checkSessionAccess(session: Session, userProfile: UserProfile | null): AccessResult {
    // Si pas d'utilisateur, refuser l'accès
    if (!userProfile) {
      return {
        allowed: false,
        reason: 'Connexion requise',
        upgradeRequired: false
      };
    }

    // Si la session n'est pas premium, autoriser l'accès
    if (!session.isPremium) {
      return { allowed: true };
    }

    // Vérifier si l'utilisateur a accès premium
    if (this.hasActivePremium(userProfile)) {
      return { allowed: true };
    }

    // Session premium mais utilisateur gratuit
    return {
      allowed: false,
      reason: 'Session premium - Mise à niveau requise',
      upgradeRequired: true,
      trialAvailable: !userProfile.subscription.isTrialActive && userProfile.subscription.plan === 'free'
    };
  }

  /**
   * Vérifier l'accès aux fonctionnalités audio avancées
   */
  checkAudioFeatureAccess(feature: 'binaural' | 'ambient' | 'premium', userProfile: UserProfile | null): AccessResult {
    if (!userProfile) {
      return {
        allowed: false,
        reason: 'Connexion requise',
        upgradeRequired: false
      };
    }

    // Les fonctionnalités audio avancées sont exclusivement premium
    if (feature === 'binaural' || feature === 'ambient' || feature === 'premium') {
      if (this.hasActivePremium(userProfile)) {
        return { allowed: true };
      }

      return {
        allowed: false,
        reason: 'Fonctionnalité premium - Mise à niveau requise',
        upgradeRequired: true,
        trialAvailable: !userProfile.subscription.isTrialActive && userProfile.subscription.plan === 'free'
      };
    }

    // Fonctionnalités de base autorisées
    return { allowed: true };
  }

  /**
   * Vérifier l'accès aux jeux
   */
  checkGameAccess(gameId: string, userProfile: UserProfile | null): AccessResult {
    if (!userProfile) {
      return {
        allowed: false,
        reason: 'Connexion requise',
        upgradeRequired: false
      };
    }

    // Les jeux sont premium
    if (this.hasActivePremium(userProfile)) {
      return { allowed: true };
    }

    return {
      allowed: false,
      reason: 'Jeux premium - Mise à niveau requise',
      upgradeRequired: true,
      trialAvailable: !userProfile.subscription.isTrialActive && userProfile.subscription.plan === 'free'
    };
  }

  /**
   * Obtenir les fonctionnalités accessibles pour un utilisateur
   */
  getFeatureAccess(userProfile: UserProfile | null): FeatureAccess {
    const isPremium = userProfile ? this.hasActivePremium(userProfile) : false;

    return {
      sessions: {
        premium: isPremium,
        unlimited: isPremium,
        types: isPremium 
          ? ['meditation', 'story', 'hypnosis', 'pnl', 'relaxation', 'breathing', 'custom']
          : ['meditation', 'story']
      },
      audio: {
        binaural: isPremium,
        ambient: isPremium,
        premium: isPremium
      },
      features: {
        noAds: isPremium,
        offline: isPremium,
        customization: isPremium
      }
    };
  }

  /**
   * Filtrer les sessions selon l'accès utilisateur
   */
  filterSessionsByAccess(sessions: Session[], userProfile: UserProfile | null): Session[] {
    if (!userProfile) {
      // Utilisateur non connecté : seulement les sessions gratuites
      return sessions.filter(session => !session.isPremium);
    }

    if (this.hasActivePremium(userProfile)) {
      // Utilisateur premium : toutes les sessions
      return sessions;
    }

    // Utilisateur gratuit : seulement les sessions gratuites
    return sessions.filter(session => !session.isPremium);
  }

  /**
   * Obtenir les sessions premium pour affichage avec verrouillage
   */
  getLockedPremiumSessions(sessions: Session[], userProfile: UserProfile | null): Session[] {
    if (!userProfile || this.hasActivePremium(userProfile)) {
      return [];
    }

    return sessions.filter(session => session.isPremium);
  }

  /**
   * Vérifier si l'utilisateur peut voir les publicités (pour débloquer du contenu)
   */
  canWatchAdsForUnlock(userProfile: UserProfile | null): boolean {
    if (!userProfile) return false;
    
    // Les utilisateurs gratuits peuvent regarder des pubs pour débloquer temporairement
    return userProfile.subscription.plan === 'free' && !userProfile.subscription.isTrialActive;
  }

  /**
   * Calculer le temps de déverrouillage temporaire après une publicité
   */
  getAdUnlockDuration(): number {
    return 30 * 60 * 1000; // 30 minutes en millisecondes
  }

  /**
   * Vérifier si l'utilisateur a un accès premium actif
   */
  private hasActivePremium(userProfile: UserProfile): boolean {
    const { plan, status, isTrialActive } = userProfile.subscription;
    
    // Premium actif ou essai actif
    return (plan === 'premium' && (status === 'active' || status === 'trial')) || isTrialActive;
  }

  /**
   * Obtenir le message d'encouragement pour la mise à niveau
   */
  getUpgradeMessage(feature: string, userProfile: UserProfile | null): string {
    if (!userProfile) {
      return 'Connectez-vous pour accéder à cette fonctionnalité';
    }

    if (userProfile.subscription.isTrialActive) {
      const remainingDays = this.getRemainingTrialDays(userProfile);
      return `Profitez de votre essai premium (${remainingDays} jours restants)`;
    }

    if (userProfile.subscription.plan === 'free') {
      return `Passez à Premium pour débloquer ${feature} et bien plus encore !`;
    }

    return 'Mise à niveau requise pour cette fonctionnalité';
  }

  /**
   * Calculer les jours d'essai restants
   */
  private getRemainingTrialDays(userProfile: UserProfile): number {
    if (!userProfile.subscription.isTrialActive || !userProfile.subscription.trialEndDate) {
      return 0;
    }
    
    const now = new Date();
    const trialEnd = userProfile.subscription.trialEndDate;
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }

  /**
   * Vérifier si une fonctionnalité nécessite une connexion
   */
  requiresAuthentication(feature: string): boolean {
    const authRequiredFeatures = [
      'premium-sessions',
      'binaural-audio',
      'ambient-audio',
      'games',
      'profile',
      'history',
      'favorites'
    ];

    return authRequiredFeatures.includes(feature);
  }

  /**
   * Obtenir les limites pour un plan donné
   */
  getPlanLimits(plan: SubscriptionPlan): {
    sessionsPerDay: number;
    maxSessionDuration: number;
    audioFeatures: string[];
    gamesAccess: boolean;
  } {
    switch (plan) {
      case 'premium':
        return {
          sessionsPerDay: -1, // Illimité
          maxSessionDuration: -1, // Illimité
          audioFeatures: ['basic', 'binaural', 'ambient', 'premium'],
          gamesAccess: true
        };
      
      case 'free':
      default:
        return {
          sessionsPerDay: 3,
          maxSessionDuration: 15 * 60, // 15 minutes
          audioFeatures: ['basic'],
          gamesAccess: false
        };
    }
  }
}

// Instance singleton
export const accessControlService = AccessControlService.getInstance();
