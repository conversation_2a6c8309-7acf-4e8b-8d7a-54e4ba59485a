// theme.ts (ou le nom de votre fichier de thème)

import { DefaultTheme } from 'styled-components';

// Assurez-vous que votre fichier styled.d.ts définit bien toutes ces propriétés
// y compris heroOverlay et errorColor si vous les utilisez.

// Exemple de styled.d.ts à avoir :
/*
import 'styled-components';

declare module 'styled-components' {
  export interface DefaultTheme {
    background: string;
    surface: string;
    primary: string;
    secondary: string;
    accent: string;
    text: string;
    textSecondary: string;
    textLight: string; // Texte clair sur fond coloré/sombre
    textLightOnPrimary?: string; // Optionnel: texte spécifique pour fond primaire si textLight ne convient pas
    border: string;
    cardShadow: string;
    headerShadow: string;
    logoBg: string;
    navActive: string;
    navInactive: string;
    surfaceAlt: string;
    gradientStart: string;
    gradientEnd: string;
    inputBackground?: string;
    textMuted?: string;
    disabled?: string;
    heroOverlay?: string; // Pour la superposition sur les images Hero
    errorColor?: string; // Pour les messages d'erreur
  }
}
*/


export const lightTheme: DefaultTheme = {
  name: 'light',
  // Couleurs principales
  background: '#f8f9fa', // Fond général de l'application
  surface: '#ffffff',    // Fond des cartes, modales, etc.
  primary: '#8A63D2',    // Violet principal (anciennement vert)
  secondary: '#6c757d',  // Gris pour éléments secondaires
  accent: '#B084CC',     // Mauve/violet plus clair, pour accents, gradients

  // Texte
  text: '#212529',         // Couleur de texte principale (plus foncée pour contraste sur fond clair)
  textSecondary: '#495057', // Texte pour descriptions, labels (plus foncé que #6c757d)
  textLight: '#FFFFFF',     // Texte à utiliser sur des fonds primaires ou sombres (ex: boutons primaires)
  textMuted: '#868e96',     // Texte très discret (anciennement #adb5bd)

  // Éléments d'interface
  border: '#dee2e6',       // Bordures (plus clair que #e9ecef)
  cardShadow: '0 4px 12px rgba(0, 0, 0, 0.08)', // Ombre plus douce et diffuse
  headerShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
  borderSlight: '#e9ecef', // Une version plus claire de votre bordure standard
  shadowSmall: '0 2px 4px rgba(0,0,0,0.05)', // Une ombre plus petite

  // Éléments spécifiques
  logoBg: '#8A63D2',
  navActive: '#8A63D2',
  navInactive: '#adb5bd',

  // Arrière-plans spéciaux
  surfaceAlt: '#f1f3f5',   // Fond alternatif légèrement différent de `surface` ou `background`
  gradientStart: '#8A63D2',
  gradientEnd: '#B084CC',

  // --- Propriétés pour formulaires et états ---
  inputBackground: '#ffffff', // Peut être identique à surface ou légèrement différent
  disabled: '#ced4da',        // Couleur pour les éléments désactivés (fond ou texte)
  
  // --- Propriétés spécifiques ajoutées pour les composants ---
  heroOverlay: 'rgba(50, 20, 80, 0.35)', // Superposition pour HeroSection (plus de violet)
  errorColor: '#d9534f', // Un rouge pour les erreurs

  // --- Propriétés pour les jeux ---
  hoverShadow: '0 10px 40px rgba(0,0,0,0.15)', // Exemple
    zenTetrisPiece1: '#2ecc71',
    zenTetrisPiece2: '#3498db',
    zenTetrisPiece3: '#9b59b6',
    zenTetrisPiece4: '#f1c40f',
    zenTetrisPiece5: '#e67e22',
    zenTetrisPiece6: '#e74c3c',
    zenTetrisPiece7: '#1abc9c',
    zenTetrisBackgroundCell: '#34495e',
    zenTetrisBoardBackground: '#2c3e50'

};

export const darkTheme: DefaultTheme = {
  name: 'dark', 
  // Couleurs principales
  background: '#1A1A1D', // Fond général plus sombre
  surface: '#2C2F33',    // Fond des cartes (plus clair que background)
  primary: '#1ECB6B',    // Vert Piknowkyo conservé pour le thème sombre
  secondary: '#99AAB5',  // Gris plus clair pour contraste
  accent: '#23D5AB',     // Vert plus clair/turquoise pour accent

  // Texte
  text: '#F0F2F5',         // Texte principal (très clair)
  textSecondary: '#B0B8BF', // Texte secondaire (un peu moins clair)
  textLight: '#FFFFFF',     // Utilisé pour les textes sur les boutons primaires (si le fond primaire est sombre)
  textMuted: '#72767D',     // Texte très discret

  // Éléments d'interface
  border: '#40444B',       // Bordures plus subtiles
  cardShadow: '0 4px 12px rgba(0, 0, 0, 0.25)', // Ombre plus prononcée sur fond sombre
  headerShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
  borderSlight: '#343a40', // Une version plus sombre adaptée
  shadowSmall: '0 2px 4px rgba(0,0,0,0.1)',  // Une ombre plus petite adaptée

  // Éléments spécifiques
  logoBg: '#1ECB6B',
  navActive: '#1ECB6B',
  navInactive: '#72767D',

  // Arrière-plans spéciaux
  surfaceAlt: '#23272A',   // Fond alternatif très proche de surface
  gradientStart: '#1ECB6B',
  gradientEnd: '#23D5AB',

  // --- Propriétés pour formulaires et états ---
  inputBackground: '#23272A', // Fond des inputs
  disabled: '#4F545C',        // Couleur pour éléments désactivés

  // --- Propriétés spécifiques ajoutées pour les composants ---
  heroOverlay: 'rgba(0, 0, 0, 0.5)', // Superposition plus sombre pour HeroSection
  errorColor: '#F07178', // Un rouge plus clair pour les erreurs sur fond sombre

  // --- Propriétés pour les jeux ---
  hoverShadow: '0 10px 40px rgba(0,0,0,0.25)', // Exemple
    zenTetrisPiece1: '#2ecc71',
    zenTetrisPiece2: '#3498db',
    zenTetrisPiece3: '#9b59b6',
    zenTetrisPiece4: '#f1c40f',
    zenTetrisPiece5: '#e67e22',
    zenTetrisPiece6: '#e74c3c',
    zenTetrisPiece7: '#1abc9c',
    zenTetrisBackgroundCell: '#34495e',
    zenTetrisBoardBackground: '#2c3e50'

};