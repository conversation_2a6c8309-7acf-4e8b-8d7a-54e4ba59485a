import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { MonetizationState, SubscriptionPlan, TemporaryUnlock } from '../../models';

const initialState: MonetizationState = {
  currentPlan: 'free',
  premiumPrice: 9, // Prix par défaut, sera configurable
  premiumCurrency: '$',
  temporaryUnlocks: [],
  isProcessingPayment: false,
};

const monetizationSlice = createSlice({
  name: 'monetization',
  initialState,
  reducers: {
    setSubscriptionPlan: (state, action: PayloadAction<SubscriptionPlan>) => {
      state.currentPlan = action.payload;
    },
    setPremiumPrice: (state, action: PayloadAction<{ price: number; currency: string }>) => {
      state.premiumPrice = action.payload.price;
      state.premiumCurrency = action.payload.currency;
    },
    addTemporaryUnlock: (state, action: PayloadAction<TemporaryUnlock>) => {
      // Supprimer les unlocks expirés
      const now = Date.now();
      state.temporaryUnlocks = state.temporaryUnlocks.filter(unlock => unlock.expiresAt > now);
      
      // Ajouter le nouvel unlock
      state.temporaryUnlocks.push(action.payload);
    },
    removeExpiredUnlocks: (state) => {
      const now = Date.now();
      state.temporaryUnlocks = state.temporaryUnlocks.filter(unlock => unlock.expiresAt > now);
    },
    clearTemporaryUnlocks: (state) => {
      state.temporaryUnlocks = [];
    },
    setProcessingPayment: (state, action: PayloadAction<boolean>) => {
      state.isProcessingPayment = action.payload;
    },
  },
});

export const {
  setSubscriptionPlan,
  setPremiumPrice,
  addTemporaryUnlock,
  removeExpiredUnlocks,
  clearTemporaryUnlocks,
  setProcessingPayment,
} = monetizationSlice.actions;

export default monetizationSlice.reducer;
