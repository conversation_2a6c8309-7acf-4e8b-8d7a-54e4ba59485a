// src/pages/PlayerPage.tsx

import React, { useEffect, useState, useContext, useRef, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import styled, { keyframes, css, ThemeContext, DefaultTheme } from 'styled-components'; // DefaultTheme est importé mais pas explicitement utilisé, ThemeContext non plus
import { fetchSessionWithScript } from '../data/sessions';
import { Session, ScriptLine, SessionAudioConfig } from '../models'; // SessionAudioConfig importé mais pas directement utilisé comme type ici
import { useLang, Language } from '../LangProvider'; // Language importé mais pas utilisé
import { useTranslation } from 'react-i18next';
import {
  FiPlay, FiPause, FiChevronLeft, FiLoader, FiVolume2, FiMusic, FiRadio, FiSettings, FiMessageCircle, FiMaximize, FiMinimize, FiRefreshCw, FiLock, FiStar, FiZap
} from 'react-icons/fi';
import { ttsPlay, ttsStop, TTSConfig, TTSProvider } from '../services/tts';
import { useAppSelector } from '../store/hooks';
import { MonetizationService } from '../services/monetizationService';
import MonetizationModal from '../components/MonetizationModal';
import UnlockTimer from '../components/UnlockTimer';

// --- Fullscreen API Helper (inchangé) ---
const isFullScreen = () => document.fullscreenElement != null;
const requestFullScreen = (element: HTMLElement) => {
  if (element.requestFullscreen) element.requestFullscreen().catch(err => console.error("Fullscreen error:", err));
  else if ((element as any).mozRequestFullScreen) (element as any).mozRequestFullScreen();
  else if ((element as any).webkitRequestFullscreen) (element as any).webkitRequestFullscreen();
  else if ((element as any).msRequestFullscreen) (element as any).msRequestFullscreen();
};
const exitFullScreen = () => {
  if (document.exitFullscreen) document.exitFullscreen().catch(err => console.error("Exit fullscreen error:", err));
  else if ((document as any).mozCancelFullScreen) (document as any).mozCancelFullScreen();
  else if ((document as any).webkitExitFullscreen) (document as any).webkitExitFullscreen();
  else if ((document as any).msExitFullscreen) (document as any).msExitFullscreen();
};

// --- Styled Components (inchangés, gardez les vôtres) ---
const PlayerPageContainer = styled.div<{ $isPlaying?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  padding: 1rem;
  text-align: center;
  color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 2000;
  background: ${({ theme, $isPlaying }) =>
    $isPlaying
      ? `linear-gradient(-45deg, ${theme.accent || '#B084CC'}, ${theme.primary || '#8A63D2'}, #23a6d5, #23d5ab)`
      : theme.background
  };
  background-size: ${({ $isPlaying }) => $isPlaying ? '400% 400%' : 'cover'};
  animation: ${({ $isPlaying }) => $isPlaying ? css`${relaxingGradient} 18s ease infinite` : 'none'};
  transition: background 0.5s ease-in-out;
`;

const BackgroundVideo = styled.video`
  position: fixed;
  right: 0;
  bottom: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -1;
  object-fit: cover;
  filter: brightness(0.6) blur(3px);
`;

const ContentOverlay = styled.div`
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  height: 100%;
  width: 100%;
  max-width: 800px;
  padding: 2rem 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
`;


const TopControls = styled.div`
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  right: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
`;

const BackButtonPlayer = styled.button`
  background: rgba(255,255,255,0.15);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  width: auto; /* Ajuster pour le texte */
  padding: 0 1rem;
  height: 44px;
  font-size: 0.9rem; /* Ajuster la taille de la police */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  gap: 0.5rem; /* Espace entre l'icône et le texte */
  &:hover {
    background: rgba(255,255,255,0.25);
  }
`;

const FullscreenButton = styled.button`
  background: rgba(255,255,255,0.15);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  &:hover {
    background: rgba(255,255,255,0.25);
  }
`;

const SessionTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 5px rgba(0,0,0,0.5);
  color: ${({ theme }) => theme.textLight || '#f0f0f0'};
`;

const CurrentScriptText = styled.p`
  font-size: 1.4rem;
  font-weight: 300;
  line-height: 1.9;
  margin: 1rem 0;
  padding: 1rem;
  min-height: 150px;
  max-width: 90%;
  color: ${({ theme }) => theme.textLight || '#e0e0e0'};
  text-shadow: 0 1px 3px rgba(0,0,0,0.4);
  overflow-y: auto;
  max-height: 40vh;
`;

const ControlsContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
`;

const MainControls = styled.div`
  display: flex;
  gap: 1.5rem;
  align-items: center;

  button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.4);
    border-radius: 50%;
    width: 70px;
    height: 70px;
    font-size: 2.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: background-color 0.2s, transform 0.1s;
    &:hover {
      background: rgba(255,255,255,0.3);
    }
    &:active {
      transform: scale(0.95);
    }
    &.restart-button {
      width: 50px;
      height: 50px;
      font-size: 1.5rem;
      background: rgba(255,255,255,0.15);
       &:hover {
        background: rgba(255,255,255,0.25);
      }
    }
  }
`;

const VolumePopupButton = styled.button`
  background: rgba(255,255,255,0.15);
  color: #e0e0e0;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 20px;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
   &:hover {
    background: rgba(255,255,255,0.25);
  }
`;

const VolumeControlPanel = styled.div<{ $show?: boolean }>`
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 0px 20px rgba(0,0,0,0.2);
  z-index: 15;
  width: 300px;
  max-width: 90vw;
  opacity: ${({ $show }) => $show ? 1 : 0};
  visibility: ${({ $show }) => $show ? 'visible' : 'hidden'};
  transition: opacity 0.3s, visibility 0s linear ${({ $show }) => $show ? '0s' : '0.3s'};
  border: 1px solid ${({ theme }) => theme.border};

  h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: ${({ theme }) => theme.primary};
    text-align: left;
  }
`;

const VolumeSliderGroup = styled.div`
  margin-bottom: 1rem;
  text-align: left;
  &:last-child {
    margin-bottom: 0;
  }
  label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.3rem;
  }
  input[type="range"] {
    width: 100%;
  }
`;

const LoadingMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.textLight || '#fff'};
  svg { font-size: 2.5rem; margin-bottom: 1rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;
const ErrorMessageStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  min-height: 50vh;
  color: ${({ theme }) => theme.textLight || '#fff'};
  p {
    color: ${({ theme }) => theme.errorColor || '#ff6b6b'};
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
  a {
    color: ${({ theme }) => theme.textLight || '#fff'};
    text-decoration: underline;
    font-weight: 500;
    &:hover {
      opacity: 0.8;
    }
  }
`;

const relaxingGradient = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

const PlayerPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { lang: appLang } = useLang();
  const { t } = useTranslation();
  const playerPageRef = useRef<HTMLDivElement>(null);
  const volumeControlPanelRef = useRef<HTMLDivElement>(null);

  // Redux state pour la monétisation
  const { currentPlan, temporaryUnlocks } = useAppSelector(state => state.monetization);

  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMonetizationModal, setShowMonetizationModal] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);

  const [currentScriptIndex, setCurrentScriptIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showVolumeControls, setShowVolumeControls] = useState(false);
  const [isInFullScreen, setIsInFullScreen] = useState(isFullScreen());

  // TTS Config
  const [ttsProvider, setTtsProvider] = useState<TTSProvider>('browser');
  const [ttsVoice, setTtsVoice] = useState<string>('auto');

  // Audio Config States (valeurs par défaut, seront surchargées)
  const [enableMusic, setEnableMusic] = useState(false);
  const [musicVolume, setMusicVolume] = useState(0.5);
  const [musicFileUrl, setMusicFileUrl] = useState<string | undefined>(undefined);

  const [enableAmbient, setEnableAmbient] = useState(false);
  const [ambientVolume, setAmbientVolume] = useState(0.3);
  const [ambientFileUrl, setAmbientFileUrl] = useState<string | undefined>(undefined);

  const [enableBinaural, setEnableBinaural] = useState(false);
  const [binauralVolume, setBinauralVolume] = useState(0.2);
  const [binauralBaseFreq, setBinauralBaseFreq] = useState(100);
  const [binauralBeatFreq, setBinauralBeatFreq] = useState(10);

  const [voiceVolume, setVoiceVolume] = useState(1);

  // Audio Refs
  const musicAudioRef = useRef<HTMLAudioElement | null>(null);
  const ambientAudioRef = useRef<HTMLAudioElement | null>(null);
  const ttsAbortController = useRef<AbortController | null>(null);
  const scriptPlayerTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Binaural Refs
  const audioCtxRef = useRef<AudioContext | null>(null);
  const binauralOscs = useRef<{left?: OscillatorNode, right?: OscillatorNode, gain?: GainNode}>({});

  const mountedRef = useRef(true); // Pour éviter les setStates sur un composant démonté

  const toggleFullScreen = useCallback(() => {
    if (!playerPageRef.current) return;
    if (isFullScreen()) exitFullScreen();
    else requestFullScreen(playerPageRef.current);
  }, []);

  // Effet pour le plein écran au démarrage et gestion des changements
  useEffect(() => {
    if (playerPageRef.current && !isFullScreen()) { // Demander seulement si pas déjà en plein écran
      // Optionnel : mettre un délai ou demander à l'utilisateur avant de forcer le plein écran
      // requestFullScreen(playerPageRef.current);
    }
    const handleChange = () => setIsInFullScreen(isFullScreen());
    document.addEventListener('fullscreenchange', handleChange);
    document.addEventListener('webkitfullscreenchange', handleChange);
    document.addEventListener('mozfullscreenchange', handleChange);
    document.addEventListener('MSFullscreenChange', handleChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleChange);
      document.removeEventListener('webkitfullscreenchange', handleChange);
      document.removeEventListener('mozfullscreenchange', handleChange);
      document.removeEventListener('MSFullscreenChange', handleChange);
    };
  }, []); // Se lance une fois au montage

  // Effet pour fermer le popup de volume en cliquant à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showVolumeControls && volumeControlPanelRef.current && !volumeControlPanelRef.current.contains(event.target as Node)) {
        setShowVolumeControls(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showVolumeControls]);

  // Effet pour charger les données de session et les configurations audio
  useEffect(() => {
    if (!sessionId) {
      setError(t('errors.missingSessionId', "ID de session invalide."));
      setIsLoading(false);
      return;
    }
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const sessionData = await fetchSessionWithScript(sessionId, appLang);
        if (sessionData) {
          // Vérifier les restrictions de monétisation
          const canAccess = MonetizationService.canAccessSession(
            sessionData.type,
            sessionData.id,
            currentPlan,
            temporaryUnlocks
          );

          if (!canAccess) {
            setShowMonetizationModal(true);
            setIsLoading(false);
            return;
          }

          setSession(sessionData);

          try {
            const savedTtsConfig = localStorage.getItem('tts_config_v2');
            if (savedTtsConfig) {
              const parsedTts = JSON.parse(savedTtsConfig);
              setTtsProvider(parsedTts.provider || 'browser');
              setTtsVoice(parsedTts.voice || 'auto');
            }
          } catch (e) { console.error("Global TTS config parsing error:", e); }

          let initialEnableMusic = sessionData.audio?.enableMusic ?? false;
          let initialMusicVolume = sessionData.audio?.music?.volume ?? 0.5;
          let initialMusicFileUrl = sessionData.audio?.music?.url;

          let initialEnableAmbient = sessionData.audio?.enableAmbient ?? false;
          let initialAmbientVolume = sessionData.audio?.ambient?.volume ?? 0.3;
          let initialAmbientFileUrl = sessionData.audio?.ambient?.url;

          let initialEnableBinaural = sessionData.audio?.enableBinaural ?? false;
          let initialBinauralVolume = sessionData.audio?.binaural?.volume ?? 0.2;
          let initialBinauralBaseFreq = sessionData.audio?.binaural?.baseFreq ?? 100;
          let initialBinauralBeatFreq = sessionData.audio?.binaural?.beatFreq ?? 10;

          let initialVoiceVolume = sessionData.audio?.voice?.volume ?? 1;

          const savedAudioOverridesRaw = localStorage.getItem(`session_audio_overrides_${sessionId}`);
          if (savedAudioOverridesRaw) {
            try {
              const parsedOverrides = JSON.parse(savedAudioOverridesRaw);
              initialEnableMusic = parsedOverrides.enableMusic ?? initialEnableMusic;
              initialMusicVolume = parsedOverrides.musicVolume ?? initialMusicVolume;
              initialMusicFileUrl = parsedOverrides.musicFileUrl ?? initialMusicFileUrl;

              initialEnableAmbient = parsedOverrides.enableAmbient ?? initialEnableAmbient;
              initialAmbientVolume = parsedOverrides.ambientVolume ?? initialAmbientVolume;
              initialAmbientFileUrl = parsedOverrides.ambientFileUrl ?? initialAmbientFileUrl;

              initialEnableBinaural = parsedOverrides.enableBinaural ?? initialEnableBinaural;
              initialBinauralVolume = parsedOverrides.binauralVolume ?? initialBinauralVolume;
              initialBinauralBaseFreq = parsedOverrides.baseFreq ?? initialBinauralBaseFreq;
              initialBinauralBeatFreq = parsedOverrides.beatFreq ?? initialBinauralBeatFreq;

              initialVoiceVolume = parsedOverrides.voiceVolume ?? initialVoiceVolume;
            } catch (e) { console.error("Session audio overrides parsing error:", e); }
          }

          // Appliquer les restrictions de monétisation aux fonctionnalités audio
          const canUseMusic = MonetizationService.canAccessAudioFeature('music', currentPlan);
          const canUseAmbient = MonetizationService.canAccessAudioFeature('ambient', currentPlan);
          const canUseBinaural = MonetizationService.canAccessAudioFeature('binaural', currentPlan);

          setEnableMusic(initialEnableMusic && canUseMusic);
          setMusicVolume(initialMusicVolume);
          setMusicFileUrl(canUseMusic ? initialMusicFileUrl : undefined);

          setEnableAmbient(initialEnableAmbient && canUseAmbient);
          setAmbientVolume(initialAmbientVolume);
          setAmbientFileUrl(canUseAmbient ? initialAmbientFileUrl : undefined);

          setEnableBinaural(initialEnableBinaural && canUseBinaural);
          setBinauralVolume(initialBinauralVolume);
          setBinauralBaseFreq(initialBinauralBaseFreq);
          setBinauralBeatFreq(initialBinauralBeatFreq);
          setVoiceVolume(initialVoiceVolume);
        } else {
          setError(t('errors.sessionNotFound', "Session non trouvée."));
        }
      } catch (e) {
        setError(t('errors.cantLoadSession', "Erreur lors du chargement de la session."));
        console.error(e);
      } finally {
        setIsLoading(false);
      }
    };
    loadData();
  }, [sessionId, appLang, t, currentPlan, temporaryUnlocks]);

  // --- Logique Binaurale ---
  const stopBinauralSound = useCallback(() => {
    if (audioCtxRef.current) {
      try {
        binauralOscs.current.left?.stop();
        binauralOscs.current.right?.stop();
        if (audioCtxRef.current.state !== 'closed') {
          audioCtxRef.current.close().catch(e => console.warn("Erreur fermeture AudioContext:", e));
        }
      } catch (e) { console.warn("Erreur arrêt/fermeture audio binaural:", e); }
      audioCtxRef.current = null;
      binauralOscs.current = {};
    }
  }, []);

  // Effet pour surveiller l'expiration des unlock temporaires
  useEffect(() => {
    if (!session || !sessionId) return;

    const canAccess = MonetizationService.canAccessSession(
      session.type,
      session.id,
      currentPlan,
      temporaryUnlocks
    );

    if (!canAccess && isPlaying) {
      // Arrêter la session si l'accès expire pendant la lecture
      setIsPlaying(false);
      setSessionExpired(true);

      // Arrêter tous les audios
      if (musicAudioRef.current) {
        musicAudioRef.current.pause();
      }
      if (ambientAudioRef.current) {
        ambientAudioRef.current.pause();
      }
      stopBinauralSound();

      // Arrêter la synthèse vocale
      if (ttsAbortController.current) {
        ttsAbortController.current.abort();
      }
      ttsStop();

      alert(t('player.sessionExpired', 'Votre accès temporaire à cette séance a expiré.'));
    }
  }, [session, sessionId, currentPlan, temporaryUnlocks, isPlaying, t, stopBinauralSound]);

  const playBinauralSound = useCallback(() => {
    if (audioCtxRef.current && audioCtxRef.current.state === 'running' && binauralOscs.current.left) {
      return; // Déjà en lecture
    }
    stopBinauralSound(); // Nettoyer d'abord

    const Ctx = window.AudioContext || (window as any).webkitAudioContext;
    if (!Ctx) { console.warn("Web Audio API non supportée."); return; }

    const newCtx = new Ctx();
    audioCtxRef.current = newCtx;

    const gainNode = newCtx.createGain();
    gainNode.gain.setValueAtTime(binauralVolume, newCtx.currentTime);
    gainNode.connect(newCtx.destination);

    const left = newCtx.createOscillator();
    const right = newCtx.createOscillator();
    left.type = right.type = 'sine';

    left.frequency.setValueAtTime(binauralBaseFreq, newCtx.currentTime);
    right.frequency.setValueAtTime(binauralBaseFreq + binauralBeatFreq, newCtx.currentTime);

    const merger = newCtx.createChannelMerger(2);
    left.connect(merger, 0, 0); right.connect(merger, 0, 1);
    merger.connect(gainNode);

    left.start(); right.start();
    binauralOscs.current = { left, right, gain: gainNode };
  }, [binauralVolume, binauralBaseFreq, binauralBeatFreq, stopBinauralSound]);

  const updateBinauralLiveParameters = useCallback(() => {
    if (audioCtxRef.current && audioCtxRef.current.state === 'running') {
      if (binauralOscs.current.gain) {
        binauralOscs.current.gain.gain.setValueAtTime(binauralVolume, audioCtxRef.current.currentTime);
      }
      // Mettre à jour les fréquences si elles changent pendant la lecture
      if (binauralOscs.current.left && binauralOscs.current.right) {
        const now = audioCtxRef.current.currentTime;
        binauralOscs.current.left.frequency.cancelScheduledValues(now);
        binauralOscs.current.left.frequency.setValueAtTime(binauralBaseFreq, now);
        binauralOscs.current.right.frequency.cancelScheduledValues(now);
        binauralOscs.current.right.frequency.setValueAtTime(binauralBaseFreq + binauralBeatFreq, now);
      }
    }
  }, [binauralVolume, binauralBaseFreq, binauralBeatFreq]);


  // Effet principal pour la lecture du script (TTS)
  useEffect(() => {
    if (!isPlaying || !session?.script || currentScriptIndex >= session.script.length) {
      if (isPlaying && session?.script && currentScriptIndex >= session.script.length) {
        setIsPlaying(false); // Arrêter la lecture globale si le script est terminé
        ttsStop(ttsAbortController.current);
      }
      return;
    }

    const currentLine = session.script[currentScriptIndex];
    if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();
    if (scriptPlayerTimeoutRef.current) clearTimeout(scriptPlayerTimeoutRef.current);

    ttsAbortController.current = new AbortController();
    const ttsConfig: TTSConfig = {
      volume: voiceVolume, signal: ttsAbortController.current.signal,
      rate: currentLine.rate, pitch: currentLine.pitch,
    };

    ttsPlay(ttsProvider, currentLine.text, ttsVoice, appLang, ttsConfig)
      .then(() => {
        if (mountedRef.current && isPlaying && ttsAbortController.current && !ttsAbortController.current.signal.aborted) {
          scriptPlayerTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current && isPlaying) setCurrentScriptIndex(prev => prev + 1);
          }, currentLine.pause || 1000);
        }
      })
      .catch(err => {
        if (err.name !== 'AbortError') {
          console.error("TTS Playback Error:", err);
          if (mountedRef.current && isPlaying && ttsAbortController.current && !ttsAbortController.current.signal.aborted) {
            scriptPlayerTimeoutRef.current = setTimeout(() => {
              if (mountedRef.current && isPlaying) setCurrentScriptIndex(prev => prev + 1);
            }, currentLine.pause || 1000);
          }
        }
      });

    return () => {
      if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();
      if (scriptPlayerTimeoutRef.current) clearTimeout(scriptPlayerTimeoutRef.current);
    };
  }, [isPlaying, currentScriptIndex, session, appLang, voiceVolume, ttsProvider, ttsVoice]);


  // Effet pour gérer la lecture des audios (musique, ambiance, binaural)
  useEffect(() => {
    const musicEl = musicAudioRef.current;
    const ambientEl = ambientAudioRef.current;

    const manageAudioFile = (audioElement: HTMLAudioElement | null, url: string | undefined, volume: number, enabled: boolean) => {
      if (!audioElement) return;
      if (url && enabled) {
        if (!audioElement.src || !audioElement.src.endsWith(url.split('/').pop()!)) { // Comparer juste le nom du fichier pour éviter problèmes avec chemins relatifs/absolus
            audioElement.src = url;
            audioElement.load();
        }
        audioElement.volume = volume;
        if (isPlaying) audioElement.play().catch(e => console.warn(`Audio play error (${url}):`, e));
        else audioElement.pause();
      } else {
        audioElement.pause();
        if(audioElement.src) audioElement.src = '';
      }
    };

    manageAudioFile(musicEl, musicFileUrl, musicVolume, enableMusic);
    manageAudioFile(ambientEl, ambientFileUrl, ambientVolume, enableAmbient);

    if (enableBinaural) {
      if (isPlaying) playBinauralSound();
      else stopBinauralSound();
    } else {
      stopBinauralSound();
    }
  }, [
    isPlaying, musicFileUrl, musicVolume, enableMusic,
    ambientFileUrl, ambientVolume, enableAmbient,
    enableBinaural, playBinauralSound, stopBinauralSound // binauralBaseFreq, binauralBeatFreq sont dans playBinauralSound deps
  ]);

  // Effets pour mettre à jour les volumes en direct
  useEffect(() => { if (musicAudioRef.current) musicAudioRef.current.volume = musicVolume; }, [musicVolume]);
  useEffect(() => { if (ambientAudioRef.current) ambientAudioRef.current.volume = ambientVolume; }, [ambientVolume]);
  useEffect(() => { updateBinauralLiveParameters(); }, [binauralVolume, binauralBaseFreq, binauralBeatFreq, updateBinauralLiveParameters]); // Ajout des fréquences si modifiables

  // Gestion du montage/démontage
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      // Nettoyage complet au démontage
      musicAudioRef.current?.pause();
      if (musicAudioRef.current) musicAudioRef.current.src = '';
      ambientAudioRef.current?.pause();
      if (ambientAudioRef.current) ambientAudioRef.current.src = '';
      stopBinauralSound();
      if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();
      if (scriptPlayerTimeoutRef.current) clearTimeout(scriptPlayerTimeoutRef.current);
      if (isFullScreen()) exitFullScreen(); // Quitter le plein écran si actif
    };
  }, [stopBinauralSound]); // stopBinauralSound est stable grâce à useCallback

  const handlePlayPause = () => {
    if (!session?.script?.length && currentScriptIndex === 0) return; // Ne rien faire si pas de script
    setIsPlaying(prev => !prev);
  };

  const handleRestart = () => {
    if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();

    // Pas besoin de pauser music/ambient/binaural ici, le changement de isPlaying le fera
    // musicAudioRef.current?.pause();
    // ambientAudioRef.current?.pause();
    // stopBinauralSound();

    setCurrentScriptIndex(0);
    setIsPlaying(false); // Cela déclenchera la pause des audios via l'effet principal
    // Optionnel: redémarrer après une courte pause pour que l'utilisateur voie le changement
    // setTimeout(() => { if (mountedRef.current) setIsPlaying(true); }, 100);
  };

  if (isLoading) return <PlayerPageContainer><LoadingMessage><FiLoader /> {t('loading.session', 'Chargement...')}</LoadingMessage></PlayerPageContainer>;
  if (error) return <PlayerPageContainer><ErrorMessageStyled><p>{error}</p> <Link to="/sessions">{t('actions.backToSessions', 'Retour aux sessions')}</Link></ErrorMessageStyled></PlayerPageContainer>;
  if (!session) return <PlayerPageContainer><ErrorMessageStyled><p>{t('errors.sessionNotFound', 'Session introuvable.')}</p> <Link to="/sessions">{t('actions.backToSessions', 'Retour aux sessions')}</Link></ErrorMessageStyled></PlayerPageContainer>;

  const isScriptEnded = currentScriptIndex >= (session.script?.length || 0);
  const currentLineText = session.script && !isScriptEnded
                          ? session.script[currentScriptIndex].text
                          : (isScriptEnded ? t('player.sessionEnded', "Fin de la séance.") : t('player.readyToStart', "Prêt à commencer..."));

  const playPauseButtonTitle = isPlaying && !isScriptEnded ? t('actions.pause', "Pause") : t('actions.play', "Lecture");
  const playPauseButtonIcon = isPlaying && !isScriptEnded ? <FiPause /> : <FiPlay />;


  return (
    <PlayerPageContainer ref={playerPageRef} $isPlaying={isPlaying && !isScriptEnded}>


      <ContentOverlay>
        <TopControls>
          <BackButtonPlayer
            onClick={() => navigate(`/sessions/${sessionId}#journal`)}
            title={t('actions.backToSessionDetails', "Retour aux détails de la séance") || "Retour aux détails"} // Ajuster le title
          >
            <FiChevronLeft />
          </BackButtonPlayer>

          {/* Afficher le timer d'unlock si actif */}
          {currentPlan === 'free' && (
            <UnlockTimer
              type="session"
              sessionId={sessionId}
              onExpired={() => {
                setSessionExpired(true);
                setIsPlaying(false);
              }}
              showExtendOption={true}
              onExtend={() => setShowMonetizationModal(true)}
            />
          )}

          <FullscreenButton onClick={toggleFullScreen} title={isInFullScreen ? t('actions.exitFullscreen', "Quitter plein écran") : t('actions.enterFullscreen', "Plein écran")}>
            {isInFullScreen ? <FiMinimize /> : <FiMaximize />}
          </FullscreenButton>
        </TopControls>

        <SessionTitle>{session.title}</SessionTitle>
        <CurrentScriptText>{currentLineText}</CurrentScriptText>

        <ControlsContainer>
          <VolumePopupButton onClick={() => setShowVolumeControls(prev => !prev)}>
            <FiSettings /> {t('player.audioSettings', 'Volumes')}
          </VolumePopupButton>

          <VolumeControlPanel $show={showVolumeControls} ref={volumeControlPanelRef}>
            <h4>{t('player.volumeControls', 'Réglages des Volumes')}</h4>
            {enableMusic && musicFileUrl && (
              <VolumeSliderGroup>
                <label htmlFor="music-vol-player"><FiMusic /> {t('player.music', 'Musique')}: {Math.round(musicVolume * 100)}%</label>
                <input id="music-vol-player" type="range" min="0" max="1" step="0.01" value={musicVolume} onChange={(e) => setMusicVolume(Number(e.target.value))} />
              </VolumeSliderGroup>
            )}
            {enableAmbient && ambientFileUrl && (
              <VolumeSliderGroup>
                <label htmlFor="ambient-vol-player"><FiRadio /> {t('player.ambient', 'Ambiance')}: {Math.round(ambientVolume * 100)}%</label>
                <input id="ambient-vol-player" type="range" min="0" max="1" step="0.01" value={ambientVolume} onChange={(e) => setAmbientVolume(Number(e.target.value))} />
              </VolumeSliderGroup>
            )}
            {enableBinaural && ( /* Afficher même si pas de session.audio?.binaural, car les fréquences sont gérées localement */
              <VolumeSliderGroup>
                <label htmlFor="binaural-vol-player"><FiVolume2 /> {t('player.binaural', 'Binaural')}: {Math.round(binauralVolume * 100)}%</label>
                <input id="binaural-vol-player" type="range" min="0" max="1" step="0.01" value={binauralVolume} onChange={(e) => setBinauralVolume(Number(e.target.value))} />
              </VolumeSliderGroup>
            )}

            {/* Afficher les fonctionnalités verrouillées avec call-to-action */}
            {!MonetizationService.canAccessAudioFeature('ambient', currentPlan) && (
              <VolumeSliderGroup>
                <div style={{
                  background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05))',
                  border: '1px solid rgba(255, 152, 0, 0.3)',
                  borderRadius: '8px',
                  padding: '0.75rem',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  ':hover': {
                    background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.15), rgba(255, 152, 0, 0.08))',
                    transform: 'translateY(-1px)'
                  }
                }} onClick={() => setShowMonetizationModal(true)}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <FiLock style={{ color: '#ff9800' }} />
                      <span style={{ fontWeight: 600 }}>{t('player.ambientLocked', 'Sons d\'ambiance')}</span>
                      <span style={{ background: '#ff9800', color: 'white', fontSize: '0.7rem', padding: '0.1rem 0.4rem', borderRadius: '8px' }}>
                        <FiStar size={10} /> Premium
                      </span>
                    </div>
                    <FiZap style={{ color: '#ff9800' }} />
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#666', fontStyle: 'italic' }}>
                    {t('player.ambientDescription', 'Sons naturels pour une immersion totale')}
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#ff9800', fontWeight: 600, marginTop: '0.25rem' }}>
                    {t('player.clickToUpgrade', 'Cliquer pour passer à Premium')}
                  </div>
                </div>
              </VolumeSliderGroup>
            )}
            {!MonetizationService.canAccessAudioFeature('binaural', currentPlan) && (
              <VolumeSliderGroup>
                <div style={{
                  background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 152, 0, 0.05))',
                  border: '1px solid rgba(255, 152, 0, 0.3)',
                  borderRadius: '8px',
                  padding: '0.75rem',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }} onClick={() => setShowMonetizationModal(true)}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      <FiLock style={{ color: '#ff9800' }} />
                      <span style={{ fontWeight: 600 }}>{t('player.binauralLocked', 'Sons binauraux')}</span>
                      <span style={{ background: '#ff9800', color: 'white', fontSize: '0.7rem', padding: '0.1rem 0.4rem', borderRadius: '8px' }}>
                        <FiStar size={10} /> Premium
                      </span>
                    </div>
                    <FiZap style={{ color: '#ff9800' }} />
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#666', fontStyle: 'italic' }}>
                    {t('player.binauralDescription', 'Battements binauraux pour la relaxation profonde')}
                  </div>
                  <div style={{ fontSize: '0.8rem', color: '#ff9800', fontWeight: 600, marginTop: '0.25rem' }}>
                    {t('player.clickToUpgrade', 'Cliquer pour passer à Premium')}
                  </div>
                </div>
              </VolumeSliderGroup>
            )}
            <VolumeSliderGroup>
              <label htmlFor="voice-vol-player"><FiMessageCircle /> {t('player.voice', 'Voix')}: {Math.round(voiceVolume * 100)}%</label>
              <input id="voice-vol-player" type="range" min="0" max="1" step="0.01" value={voiceVolume} onChange={(e) => setVoiceVolume(Number(e.target.value))} />
            </VolumeSliderGroup>
          </VolumeControlPanel>

          <MainControls>
            <button className="restart-button" onClick={handleRestart} title={t('actions.restart', "Redémarrer") || "Redémarrer"}>
              <FiRefreshCw />
            </button>
            <button onClick={handlePlayPause} title={playPauseButtonTitle} disabled={isScriptEnded && !isPlaying /* Désactiver play si fini et pas en lecture */}>
              {playPauseButtonIcon}
            </button>
          </MainControls>
        </ControlsContainer>
      </ContentOverlay>

      <audio ref={musicAudioRef} loop />
      <audio ref={ambientAudioRef} loop />

      {/* Modal de monétisation */}
      <MonetizationModal
        isOpen={showMonetizationModal}
        onClose={() => {
          setShowMonetizationModal(false);
          if (!MonetizationService.canAccessSession(session?.type || '', session?.id || '', currentPlan, temporaryUnlocks)) {
            navigate('/sessions');
          }
        }}
        type="session"
        sessionId={sessionId}
        sessionTitle={session?.title}
        onUpgrade={() => {
          navigate('/monetization');
        }}
      />
    </PlayerPageContainer>
  );
};

export default PlayerPage;