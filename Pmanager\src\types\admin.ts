// Types pour l'administration
export interface AdminUser {
  uid: string;
  email: string;
  displayName?: string;
  role: AdminRole;
  permissions: Permission[];
  createdAt: Date;
  lastLogin?: Date;
  isActive: boolean;
}

export type AdminRole = 'super_admin' | 'admin' | 'moderator' | 'content_manager';

export type Permission = 
  | 'manage_users'
  | 'manage_subscriptions'
  | 'manage_sessions'
  | 'manage_pricing'
  | 'view_analytics'
  | 'manage_content'
  | 'manage_ai_apis'
  | 'manage_acl';

export interface SubscriptionPricing {
  id: string;
  planName: string;
  price: number;
  currency: string;
  duration: 'monthly' | 'yearly';
  features: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSubscription {
  userId: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired' | 'pending';
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  paymentMethod?: string;
}

export interface SessionTemplate {
  id: string;
  title: string;
  description: string;
  type: string;
  category: string;
  language: string;
  durationMinutes: number;
  isPremium: boolean;
  tags: string[];
  benefits: string[];
  audioConfig?: {
    music?: boolean;
    ambient?: boolean;
    binaural?: boolean;
    voice?: boolean;
  };
  script?: ScriptLine[];
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface ScriptLine {
  text: string;
  duration?: number;
  pause?: number;
  speaker?: string;
  rate?: number;
  pitch?: number;
}

export interface AIProvider {
  id: string;
  name: 'groq' | 'mistral' | 'google' | 'chutes';
  apiKey: string;
  isActive: boolean;
  config: {
    model?: string;
    maxTokens?: number;
    temperature?: number;
    [key: string]: any;
  };
  usage: {
    totalRequests: number;
    totalTokens: number;
    lastUsed?: Date;
  };
}

export interface ScriptGenerationRequest {
  sessionType: string;
  duration: number;
  language: string;
  theme: string;
  provider: 'groq' | 'mistral' | 'google' | 'chutes';
  additionalInstructions?: string;
}

export interface Analytics {
  users: {
    total: number;
    active: number;
    premium: number;
    newThisMonth: number;
  };
  sessions: {
    totalPlayed: number;
    averageDuration: number;
    mostPopular: string[];
  };
  revenue: {
    monthly: number;
    yearly: number;
    growth: number;
  };
  ai: {
    requestsThisMonth: number;
    tokensUsed: number;
    costEstimate: number;
  };
}
