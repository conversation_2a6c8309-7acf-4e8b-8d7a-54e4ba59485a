/**
 * Script d'initialisation pour créer le premier utilisateur administrateur
 * À exécuter une seule fois pour configurer l'accès initial
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, doc, setDoc } from 'firebase/firestore';
import { getAuth, createUserWithEmailAndPassword } from 'firebase/auth';

// Configuration Firebase (remplacer par vos vraies valeurs)
const firebaseConfig = {
  apiKey: "AIzaSyABy8bGDxVU-sM2nqfsp3jDm8JPtg_v4kM",
  authDomain: "piknowkyo-777.firebaseapp.com",
  projectId: "piknowkyo-777",
  storageBucket: "piknowkyo-777.firebasestorage.app",
  messagingSenderId: "375619599814",
  appId: "1:375619599814:web:9ece9c5c2ce600a8c206c7",
  measurementId: "G-DSXRMZ4JP2"
};

// Initialiser Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Données du super administrateur initial
const SUPER_ADMIN = {
  email: '<EMAIL>',
  password: 'AdminPiknowkyo2024!',
  displayName: 'Super Administrateur',
  role: 'super_admin',
  permissions: [
    'manage_users',
    'manage_subscriptions',
    'manage_sessions',
    'manage_pricing',
    'view_analytics',
    'manage_content',
    'manage_ai_apis',
    'manage_acl'
  ]
};

// Plans de tarification par défaut
const DEFAULT_PRICING_PLANS = [
  {
    planName: 'Gratuit',
    price: 0,
    currency: 'EUR',
    duration: 'monthly',
    features: [
      'Accès aux sessions de base',
      'Méditations guidées',
      'Histoires pour dormir',
      'Support communautaire'
    ],
    isActive: true
  },
  {
    planName: 'Premium Mensuel',
    price: 9.99,
    currency: 'EUR',
    duration: 'monthly',
    features: [
      'Toutes les fonctionnalités gratuites',
      'Sessions premium illimitées',
      'Sons binauraux',
      'Musiques et ambiances',
      'Hypnose et PNL',
      'Sessions personnalisées',
      'Support prioritaire',
      'Téléchargement hors ligne'
    ],
    isActive: true
  },
  {
    planName: 'Premium Annuel',
    price: 99.99,
    currency: 'EUR',
    duration: 'yearly',
    features: [
      'Toutes les fonctionnalités Premium',
      '2 mois gratuits',
      'Accès anticipé aux nouvelles fonctionnalités',
      'Sessions exclusives',
      'Coaching personnalisé'
    ],
    isActive: true
  }
];

// Fournisseurs IA par défaut
const DEFAULT_AI_PROVIDERS = [
  {
    name: 'groq',
    apiKey: '',
    isActive: false,
    config: {
      model: 'mixtral-8x7b-32768',
      maxTokens: 2000,
      temperature: 0.7
    },
    usage: {
      totalRequests: 0,
      totalTokens: 0
    }
  },
  {
    name: 'mistral',
    apiKey: '',
    isActive: false,
    config: {
      model: 'mistral-medium',
      maxTokens: 2000,
      temperature: 0.7
    },
    usage: {
      totalRequests: 0,
      totalTokens: 0
    }
  },
  {
    name: 'google',
    apiKey: '',
    isActive: false,
    config: {
      model: 'gemini-pro',
      maxTokens: 2000,
      temperature: 0.7
    },
    usage: {
      totalRequests: 0,
      totalTokens: 0
    }
  },
  {
    name: 'chutes',
    apiKey: '',
    isActive: false,
    config: {
      model: 'chutes-default',
      maxTokens: 2000,
      temperature: 0.7
    },
    usage: {
      totalRequests: 0,
      totalTokens: 0
    }
  }
];

async function initializeAdmin() {
  try {
    console.log('🚀 Initialisation de l\'administration Piknowkyo...');

    // 1. Créer le super administrateur
    console.log('👤 Création du super administrateur...');
    const userCredential = await createUserWithEmailAndPassword(
      auth, 
      SUPER_ADMIN.email, 
      SUPER_ADMIN.password
    );

    const adminUser = {
      email: SUPER_ADMIN.email,
      displayName: SUPER_ADMIN.displayName,
      role: SUPER_ADMIN.role,
      permissions: SUPER_ADMIN.permissions,
      createdAt: new Date(),
      isActive: true
    };

    await setDoc(doc(db, 'admin_users', userCredential.user.uid), adminUser);
    console.log('✅ Super administrateur créé avec succès');

    // 2. Créer les plans de tarification par défaut
    console.log('💰 Création des plans de tarification...');
    for (const plan of DEFAULT_PRICING_PLANS) {
      const planData = {
        ...plan,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const planRef = doc(db, 'subscription_pricing', `plan_${plan.planName.toLowerCase().replace(/\s+/g, '_')}`);
      await setDoc(planRef, planData);
    }
    console.log('✅ Plans de tarification créés');

    // 3. Créer les fournisseurs IA par défaut
    console.log('🤖 Création des fournisseurs IA...');
    for (const provider of DEFAULT_AI_PROVIDERS) {
      const providerRef = doc(db, 'ai_providers', provider.name);
      await setDoc(providerRef, provider);
    }
    console.log('✅ Fournisseurs IA créés');

    console.log('\n🎉 Initialisation terminée avec succès !');
    console.log('\n📋 Informations de connexion :');
    console.log(`Email: ${SUPER_ADMIN.email}`);
    console.log(`Mot de passe: ${SUPER_ADMIN.password}`);
    console.log('\n⚠️  IMPORTANT: Changez le mot de passe après la première connexion !');

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
    
    if (error.code === 'auth/email-already-in-use') {
      console.log('ℹ️  L\'utilisateur administrateur existe déjà');
    }
  }
}

// Exécuter l'initialisation
initializeAdmin();
