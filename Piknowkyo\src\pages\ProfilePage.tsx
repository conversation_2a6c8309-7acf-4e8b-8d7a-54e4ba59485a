// src/pages/ProfilePage.tsx

import React, { useState, useEffect, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useLang, Language } from '../LangProvider';
// import { useAuth } from '../AuthContext'; // Supposons un AuthContext
// import { auth, db, functions } from '../services/firebase'; // Votre instance Firebase
// import { signOut } from "firebase/auth";
// import { doc, getDoc, updateDoc, deleteDoc } from "firebase/firestore";
// import { httpsCallable } from 'firebase/functions';
import { FiUser, FiLogOut, FiTrash2, FiGlobe, FiStar, FiSettings, FiShield, FiDollarSign, FiLoader, FiAlertCircle, FiRefreshCw } from 'react-icons/fi';

// --- Types ---
type GrammaticalGender = 'masculine' | 'feminine';

interface UserProfile {
  uid: string;
  email?: string | null;
  displayName?: string | null; // Le vrai nom/pseudo si l'utilisateur en a un
  anonymousPseudo?: string; // Le pseudo affiché publiquement
  grammaticalGender?: GrammaticalGender;
  // ... autres préférences utilisateur
  isPremium?: boolean;
  // ...
}

interface CurrentUser { // Placeholder si useAuth n'est pas prêt
    uid: string;
    email?: string | null;
    displayName?: string | null;
}


// --- Styled Components ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
`;

const ProfileCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
`;

const AvatarPlaceholder = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.primary}33;
  color: ${({ theme }) => theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  margin: 0 auto 1rem auto;
  border: 3px solid ${({ theme }) => theme.primary};
`;

const AnonymousPseudoDisplay = styled.div`
  font-size: 1.2rem;
  font-weight: 600;
  color: ${({ theme }) => theme.text};
  margin-bottom: 0.25rem;
`;

const UserEmailDisplay = styled.div`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  margin-bottom: 1.5rem;
`;

const SettingsSection = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid ${({ theme }) => theme.border};
`;

const SectionTitle = styled.h2`
  font-size: 1.3rem;
  color: ${({ theme }) => theme.primary};
  margin-top: 0;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.2rem;
  label {
    display: block;
    font-weight: 500;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }
  select {
    width: 100%;
    padding: 0.7rem;
    border-radius: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground};
    color: ${({ theme }) => theme.text};
    font-size: 1rem;
    &:focus { /* ... (styles de focus) ... */ }
  }
`;

const Button = styled.button<{ $variant?: 'primary' | 'danger' | 'secondary' }>`
  background: ${({ theme, $variant }) =>
    $variant === 'danger' ? (theme.errorColor || '#d9534f') :
    $variant === 'secondary' ? theme.secondary :
    theme.primary
  };
  color: ${({ theme }) => theme.textLight || '#fff'};
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 1rem;
  margin-right: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  &:hover { opacity: 0.85; }
  &:active { transform: scale(0.97); }
  &:disabled { /* ... */ }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const InfoText = styled.p`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  margin-top: 0.5rem;
  line-height: 1.5;
`;

const PseudoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  padding: 0.8rem;
  background: ${({ theme }) => theme.surface};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
`;

const PseudoText = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.primary};
`;

const RefreshButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: all 0.2s;

  &:hover {
    background: ${({ theme }) => theme.surface};
    color: ${({ theme }) => theme.primary};
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
`;

const StatCard = styled.div`
  background: ${({ theme }) => theme.surface};
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  text-align: center;

  .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: ${({ theme }) => theme.primary};
    display: block;
  }

  .stat-label {
    font-size: 0.85rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-top: 0.25rem;
  }
`;

const ModalContent = styled.div`
  background: ${({ theme }) => theme.surface};
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  max-width: 450px;
  width: 90%;
  text-align: center;
  h3 {
    color: ${({ theme }) => theme.primary};
    margin-top: 0;
    margin-bottom: 1rem;
  }
  p {
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
  .actions {
    display: flex;
    justify-content: space-around;
    gap: 1rem;
  }
`;

const LoadingContainer = styled.div` /* ... */ `;
const ErrorMessage = styled.p` /* ... */ `;


const generateAnonymousPseudo = (uid: string, t: any): string => {
    const adjectives = [
        t('pseudoGenerator.adjectives.light', 'Lumière'),
        t('pseudoGenerator.adjectives.wind', 'Vent'),
        t('pseudoGenerator.adjectives.ocean', 'Océan'),
        t('pseudoGenerator.adjectives.mountain', 'Montagne'),
        t('pseudoGenerator.adjectives.star', 'Étoile'),
        t('pseudoGenerator.adjectives.forest', 'Forêt'),
        t('pseudoGenerator.adjectives.river', 'Rivière'),
        t('pseudoGenerator.adjectives.sun', 'Soleil'),
        t('pseudoGenerator.adjectives.moon', 'Lune'),
        t('pseudoGenerator.adjectives.aurora', 'Aurore')
    ];
    const nouns = [
        t('pseudoGenerator.nouns.serene', 'Sereine'),
        t('pseudoGenerator.nouns.calm', 'Calme'),
        t('pseudoGenerator.nouns.wise', 'Sage'),
        t('pseudoGenerator.nouns.peaceful', 'Paisible'),
        t('pseudoGenerator.nouns.clairvoyant', 'Clairvoyante'),
        t('pseudoGenerator.nouns.harmonious', 'Harmonieuse'),
        t('pseudoGenerator.nouns.awakened', 'Éveillée'),
        t('pseudoGenerator.nouns.free', 'Libre'),
        t('pseudoGenerator.nouns.creative', 'Créative'),
        t('pseudoGenerator.nouns.intuitive', 'Intuitive')
    ];
    const hash = uid.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return `${adjectives[hash % adjectives.length]} ${nouns[hash % nouns.length]}`;
};


const ProfilePage: React.FC = () => {
  const { t } = useTranslation();
  const { lang, setLang } = useLang(); // Langue globale de l'app
  const theme = useContext(ThemeContext) as DefaultTheme;
  const navigate = useNavigate();

  // REMPLACER par votre logique d'authentification réelle
  // const { currentUser, logout, deleteUserAccount } = useAuth();
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>({uid: "testUser123", email: "<EMAIL>", displayName: "Utilisateur Test"}); // Placeholder

  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [isRegeneratingPseudo, setIsRegeneratingPseudo] = useState(false);


  // Charger le profil utilisateur depuis Firestore (ou simuler)
  useEffect(() => {
    if (currentUser) {
      setIsLoading(true);
      // const userDocRef = doc(db, "userProfiles", currentUser.uid);
      // const unsubscribe = onSnapshot(userDocRef, (docSnap) => {
      //   if (docSnap.exists()) {
      //     const data = docSnap.data() as Omit<UserProfile, 'uid'>;
      //     setUserProfile({
      //       uid: currentUser.uid,
      //       email: currentUser.email,
      //       displayName: currentUser.displayName,
      //       ...data,
      //       anonymousPseudo: data.anonymousPseudo || generateAnonymousPseudo(currentUser.uid)
      //     });
      //   } else {
      //     // Créer un profil de base s'il n'existe pas
      //     const базовыйProfil: UserProfile = {
      //         uid: currentUser.uid,
      //         email: currentUser.email,
      //         displayName: currentUser.displayName,
      //         anonymousPseudo: generateAnonymousPseudo(currentUser.uid),
      //         grammaticalGender: 'neutral'
      //     };
      //     setDoc(userDocRef, { anonymousPseudo: базовыйProfil.anonymousPseudo, grammaticalGender: 'neutral' }); // Ne stocker que ce qui est modifiable
      //     setUserProfile(базовыйProfil);
      //   }
      //   setIsLoading(false);
      // }, (err) => {
      //   console.error("Error fetching user profile:", err);
      //   setError(t('errors.cantLoadProfile', "Impossible de charger le profil."));
      //   setIsLoading(false);
      // });
      // return () => unsubscribe();

      // Simulation:
      setTimeout(() => {
        const anonPseudo = generateAnonymousPseudo(currentUser.uid, t);
        setUserProfile({
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName,
          anonymousPseudo: localStorage.getItem(`anonPseudo_${currentUser.uid}`) || anonPseudo,
          grammaticalGender: (localStorage.getItem(`gender_${currentUser.uid}`) as GrammaticalGender) || 'neutral',
        });
        if (!localStorage.getItem(`anonPseudo_${currentUser.uid}`)) {
            localStorage.setItem(`anonPseudo_${currentUser.uid}`, anonPseudo);
        }
        setIsLoading(false);
      }, 500);
    } else {
      setIsLoading(false);
      // Rediriger vers la connexion si pas d'utilisateur (selon votre logique d'auth)
      // navigate('/login');
    }
  }, [currentUser, t]);


  const handleGrammaticalGenderChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newGender = e.target.value as GrammaticalGender;
    if (userProfile) {
      setUserProfile({ ...userProfile, grammaticalGender: newGender });
      // Simulation de sauvegarde
      localStorage.setItem(`gender_${userProfile.uid}`, newGender);
      console.log("Sauvegarde du genre grammatical (simulation):", newGender);
      // try {
      //   const userDocRef = doc(db, "userProfiles", userProfile.uid);
      //   await updateDoc(userDocRef, { grammaticalGender: newGender });
      //   // Afficher une notification de succès
      // } catch (error) {
      //   console.error("Erreur sauvegarde genre:", error);
      // }
    }
  };

  const handleRegeneratePseudo = async () => {
    if (!userProfile) return;

    setIsRegeneratingPseudo(true);

    // Simulation d'un délai pour l'effet visuel
    setTimeout(() => {
      const newPseudo = generateAnonymousPseudo(userProfile.uid + Date.now(), t);
      setUserProfile({ ...userProfile, anonymousPseudo: newPseudo });
      localStorage.setItem(`anonPseudo_${userProfile.uid}`, newPseudo);
      setIsRegeneratingPseudo(false);
    }, 500);
  };

  const handleLogout = async () => {
    // await logout(); // Votre fonction de déconnexion Firebase
    console.log("Déconnexion (simulation)");
    setCurrentUser(null); // Pour la démo
    setUserProfile(null);
    navigate('/'); // Rediriger vers la page d'accueil
  };

  const handleDeleteAccount = async () => {
    if (!userProfile) return;
    // Fermer la modale d'abord
    setShowDeleteConfirmModal(false);
    setIsSaving(true); // Utiliser isSaving pour l'état de chargement de la suppression
    console.log("Suppression du compte (simulation) pour:", userProfile.uid);
    // try {
    //   // 1. Appeler une Cloud Function pour supprimer les données utilisateur de Firestore/Storage
    //   // const deleteUserDataCallable = httpsCallable(functions, 'deleteUserData');
    //   // await deleteUserDataCallable(); // La fonction cloud utilise l'UID de l'utilisateur authentifié
    //   // 2. Supprimer l'utilisateur de Firebase Authentication
    //   // await deleteUserAccount(); // Votre fonction du AuthContext
    //   alert(t('profile.accountDeletedSuccess', 'Votre compte et toutes vos données ont été supprimés.'));
    //   navigate('/');
    // } catch (error) {
    //   console.error("Erreur suppression compte:", error);
    //   alert(t('errors.deleteAccountError', 'Une erreur est survenue lors de la suppression de votre compte.'));
    // } finally {
    //   setIsSaving(false);
    // }
    // Simulation
    setTimeout(() => {
        localStorage.removeItem(`anonPseudo_${userProfile.uid}`);
        localStorage.removeItem(`gender_${userProfile.uid}`);
        localStorage.removeItem('piknowkyo_journal'); // Supprimer aussi le journal
        alert(t('profile.accountDeletedSuccess', 'Votre compte et toutes vos données ont été supprimés (simulation).'));
        setCurrentUser(null);
        setUserProfile(null);
        setIsSaving(false);
        navigate('/');
    }, 1500);

  };


  if (isLoading || !userProfile && currentUser) { // Afficher chargement si userProfile n'est pas encore là mais currentUser oui
    return <LoadingContainer><FiLoader /> {t('loading.profile', 'Chargement du profil...')}</LoadingContainer>;
  }

  if (!currentUser || !userProfile) { // Si pas d'utilisateur après le "chargement"
    return (
        <PageContainer>
            <PageHeader><h1><FiUser /> {t('profile.notConnectedTitle', 'Profil Utilisateur')}</h1></PageHeader>
            <InfoText style={{textAlign: 'center'}}>
                {t('profile.pleaseLogin', 'Veuillez vous connecter pour accéder à votre profil.')}
            </InfoText>
            {/* Ajouter un bouton de connexion ici si pertinent */}
        </PageContainer>
    );
  }

  if (error) {
    return <ErrorMessage>{error}</ErrorMessage>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiUser /> {t('profile.title', 'Mon Profil')}</h1>
      </PageHeader>

      <ProfileCard>
        <AvatarPlaceholder>
          {userProfile.displayName ? userProfile.displayName.charAt(0).toUpperCase() : <FiUser />}
        </AvatarPlaceholder>

        <PseudoContainer>
          <div style={{flex: 1}}>
            <div style={{fontSize: '0.85rem', color: theme.textSecondary, marginBottom: '0.25rem'}}>
              {t('profile.publicPseudo', 'Pseudo public')}
            </div>
            <PseudoText>{userProfile.anonymousPseudo}</PseudoText>
          </div>
          <RefreshButton
            onClick={handleRegeneratePseudo}
            disabled={isRegeneratingPseudo}
            title={t('profile.regeneratePseudo', 'Générer un nouveau pseudo')}
          >
            <FiRefreshCw style={{animation: isRegeneratingPseudo ? 'spin 1s linear infinite' : 'none'}} />
          </RefreshButton>
        </PseudoContainer>

        {userProfile.email && <UserEmailDisplay>{userProfile.email}</UserEmailDisplay>}

        {/* Afficher le statut Premium si applicable */}
        {userProfile.isPremium && (
            <p style={{color: theme.primary, fontWeight: 'bold', margin: '1rem 0'}}>
              <FiStar style={{marginRight: '0.3em'}} /> {t('profile.premiumMember', 'Membre Premium')}
            </p>
        )}

        <StatsContainer>
          <StatCard>
            <span className="stat-value">42</span>
            <div className="stat-label">{t('profile.stats.sessionsCompleted', 'Séances complétées')}</div>
          </StatCard>
          <StatCard>
            <span className="stat-value">7</span>
            <div className="stat-label">{t('profile.stats.daysStreak', 'Jours consécutifs')}</div>
          </StatCard>
          <StatCard>
            <span className="stat-value">156</span>
            <div className="stat-label">{t('profile.stats.totalMinutes', 'Minutes totales')}</div>
          </StatCard>
        </StatsContainer>

         <Button $variant="secondary" onClick={() => navigate('/monetization')} style={{fontSize: '0.9rem', padding: '0.6rem 1rem', width: '100%'}}>
            <FiDollarSign /> {userProfile.isPremium ? t('profile.manageSubscription', 'Gérer l\'abonnement') : t('profile.upgradeToPremium', 'Passer à Premium')}
        </Button>
      </ProfileCard>

      <SettingsSection>
        <SectionTitle><FiSettings /> {t('profile.preferencesTitle', 'Préférences')}</SectionTitle>
        <FormGroup>
          <label htmlFor="app-language">{t('profile.appLanguage', 'Langue de l\'application :')}</label>
          <select id="app-language" value={lang} onChange={e => setLang(e.target.value as Language)}>
            <option value="fr">{t('languages.french')}</option>
            <option value="en">{t('languages.english')}</option>
            <option value="es">{t('languages.spanish')}</option>
          </select>
        </FormGroup>
        <FormGroup>
          <label htmlFor="grammatical-gender">{t('profile.grammaticalGenderLabel', 'Comment préférez-vous que l\'on s\'adresse à vous dans les scripts ?')}</label>
          <select
            id="grammatical-gender"
            value={userProfile.grammaticalGender || 'masculine'}
            onChange={handleGrammaticalGenderChange}
          >
            <option value="masculine">{t('gender.masculine', 'Au masculin')}</option>
            <option value="feminine">{t('gender.feminine', 'Au féminin')}</option>
          </select>
          <InfoText>{t('profile.grammaticalGenderInfo', 'Cela nous aidera à adapter certains textes pour une expérience plus personnalisée.')}</InfoText>
        </FormGroup>

        <div style={{marginTop: '1.5rem', padding: '1rem', background: theme.surface, borderRadius: '8px', border: `1px solid ${theme.border}`}}>
          <h4 style={{margin: '0 0 0.5rem 0', color: theme.primary}}>{t('profile.quickActions', 'Actions rapides')}</h4>
          <div style={{display: 'flex', gap: '0.5rem', flexWrap: 'wrap'}}>
            <Button $variant="secondary" onClick={() => navigate('/settings')} style={{fontSize: '0.85rem', padding: '0.5rem 1rem'}}>
              <FiSettings /> {t('profile.goToSettings', 'Paramètres avancés')}
            </Button>
            <Button $variant="secondary" onClick={() => navigate('/stats')} style={{fontSize: '0.85rem', padding: '0.5rem 1rem'}}>
              <FiStar /> {t('profile.viewStats', 'Voir mes statistiques')}
            </Button>
          </div>
        </div>

      </SettingsSection>

      <SettingsSection>
        <SectionTitle><FiShield /> {t('profile.accountActionsTitle', 'Gestion du Compte')}</SectionTitle>
        <Button onClick={handleLogout} $variant="secondary">
          <FiLogOut /> {t('profile.logout', 'Déconnexion')}
        </Button>
        <Button onClick={() => setShowDeleteConfirmModal(true)} $variant="danger">
          <FiTrash2 /> {t('profile.deleteAccount', 'Supprimer mon compte')}
        </Button>
      </SettingsSection>

      {showDeleteConfirmModal && (
        <ModalOverlay onClick={() => setShowDeleteConfirmModal(false)}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <h3><FiAlertCircle style={{marginRight: '0.5rem', color: theme.errorColor || 'red'}} />{t('profile.deleteConfirmTitle', 'Confirmer la Suppression')}</h3>
            <p>{t('profile.deleteConfirmMessage', 'Êtes-vous sûr de vouloir supprimer votre compte ? Toutes vos données, y compris votre progression et vos notes de journal, seront définitivement effacées. Cette action est irréversible.')}</p>
            <div className="actions">
              <Button onClick={() => setShowDeleteConfirmModal(false)} $variant="secondary" disabled={isSaving}>
                {t('actions.cancel', 'Annuler')}
              </Button>
              <Button onClick={handleDeleteAccount} $variant="danger" disabled={isSaving}>
                {isSaving ? <FiLoader style={{animation: 'spin 1s linear infinite'}}/> : <FiTrash2 />}
                {isSaving ? t('actions.deleting', 'Suppression...') : t('actions.deleteConfirm', 'Oui, supprimer')}
              </Button>
            </div>
          </ModalContent>
        </ModalOverlay>
      )}
    </PageContainer>
  );
};

export default ProfilePage;