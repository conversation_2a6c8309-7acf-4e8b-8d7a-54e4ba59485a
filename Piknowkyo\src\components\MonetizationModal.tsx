import React, { useState, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme, keyframes } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { FiX, FiStar, FiPlay, FiLoader, FiZap, FiClock, FiGift } from 'react-icons/fi';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { addTemporaryUnlock, setProcessingPayment } from '../store/slices/monetizationSlice';
import { MonetizationService } from '../services/monetizationService';
import { useAuth } from '../contexts/AuthContext';
import { useAccessControl } from '../hooks/useAccessControl';
import ReusableModal from './ReusableModal';

const spin = keyframes`
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
`;

const ModalContent = styled.div`
  text-align: center;
  padding: 1rem;
`;

const ModalHeader = styled.div`
  margin-bottom: 2rem;

  h2 {
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.5rem;
  }

  p {
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const OptionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;

  @media (min-width: 768px) {
    flex-direction: row;
    gap: 1.5rem;
  }
`;

const OptionCard = styled.div<{ $isPrimary?: boolean }>`
  background: ${({ theme }) => theme.surface};
  border: 2px solid ${({ theme, $isPrimary }) => $isPrimary ? theme.primary : theme.border};
  border-radius: 12px;
  padding: 1.5rem;
  flex: 1;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }

  h3 {
    color: ${({ theme, $isPrimary }) => $isPrimary ? theme.primary : theme.text};
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  p {
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
  }
`;

const ActionButton = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  ${({ $variant, theme }) => $variant === 'primary' ? `
    background: ${theme.primary};
    color: white;

    &:hover:not(:disabled) {
      background: ${theme.primaryDark || theme.primary};
      transform: translateY(-1px);
    }
  ` : `
    background: ${theme.secondary};
    color: ${theme.text};

    &:hover:not(:disabled) {
      background: ${theme.secondaryDark || theme.secondary};
    }
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const TimerInfo = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;

  .timer-text {
    color: ${({ theme }) => theme.textSecondary};
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .timer-duration {
    color: ${({ theme }) => theme.primary};
    font-weight: 600;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;

  &:hover {
    background: ${({ theme }) => theme.surfaceAlt};
    color: ${({ theme }) => theme.text};
  }
`;

interface MonetizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  type?: 'session' | 'game';
  feature?: string;
  sessionId?: string;
  sessionTitle?: string;
  onUpgrade?: () => void;
  onWatchAd?: () => void;
}

const MonetizationModal: React.FC<MonetizationModalProps> = ({
  isOpen,
  onClose,
  type = 'session',
  feature,
  sessionId,
  sessionTitle,
  onUpgrade,
  onWatchAd,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const dispatch = useAppDispatch();
  const { premiumPrice, premiumCurrency, isProcessingPayment } = useAppSelector(state => state.monetization);
  const { userProfile, hasActivePremium } = useAuth();
  const { canWatchAds } = useAccessControl();

  const [isWatchingAd, setIsWatchingAd] = useState(false);

  const handleWatchAd = async () => {
    setIsWatchingAd(true);

    try {
      // Si un callback personnalisé est fourni, l'utiliser
      if (onWatchAd) {
        onWatchAd();
        onClose();
        return;
      }

      // Sinon, utiliser l'ancien système
      const success = await MonetizationService.watchAdvertisement();

      if (success) {
        const unlock = MonetizationService.createTemporaryUnlock(type, sessionId);
        dispatch(addTemporaryUnlock(unlock));

        // Afficher un message de succès
        alert(t('monetization.adSuccess', 'Vidéo regardée avec succès ! Vous avez maintenant accès à cette fonctionnalité.'));
        onClose();
      } else {
        alert(t('monetization.adFailed', 'Erreur lors du chargement de la vidéo. Veuillez réessayer.'));
      }
    } catch (error) {
      console.error('Erreur lors du visionnage de la publicité:', error);
      alert(t('monetization.adError', 'Une erreur est survenue. Veuillez réessayer.'));
    } finally {
      setIsWatchingAd(false);
    }
  };

  const handleUpgrade = () => {
    if (onUpgrade) {
      onUpgrade();
    } else {
      // Navigation vers la page de monétisation par défaut
      navigate('/monetization');
    }
    onClose();
  };

  const getUnlockDuration = () => {
    return type === 'session' ? 4 : 1; // heures
  };

  const getModalTitle = () => {
    if (feature) {
      return t('monetization.modal.featureTitle', 'Débloquer {{feature}}', { feature });
    }
    if (type === 'session') {
      return sessionTitle
        ? t('monetization.modal.sessionTitle', 'Accéder à "{{title}}"', { title: sessionTitle })
        : t('monetization.modal.sessionTitleGeneric', 'Accéder à cette séance');
    }
    return t('monetization.modal.gameTitle', 'Accéder aux jeux');
  };

  const getModalDescription = () => {
    if (feature) {
      return t('monetization.modal.featureDescription', 'Cette fonctionnalité est réservée aux membres premium. Choisissez une option ci-dessous pour y accéder.');
    }
    if (type === 'session') {
      return t('monetization.modal.sessionDescription', 'Cette séance fait partie de nos fonctionnalités premium. Choisissez une option ci-dessous pour y accéder.');
    }
    return t('monetization.modal.gameDescription', 'Les jeux de pleine conscience sont réservés aux membres premium. Choisissez une option ci-dessous pour y accéder.');
  };

  return (
    <ReusableModal isOpen={isOpen} onClose={onClose} maxWidth="600px">
      <CloseButton onClick={onClose}>
        <FiX size={20} />
      </CloseButton>

      <ModalContent>
        <ModalHeader>
          <h2>
            <FiStar />
            {getModalTitle()}
          </h2>
          <p>{getModalDescription()}</p>
        </ModalHeader>

        <OptionsContainer>
          {canWatchAds && (
            <OptionCard>
              <h3>
                <FiPlay />
                {t('monetization.modal.watchAd', 'Regarder une vidéo')}
              </h3>
              <p>
                {t('monetization.modal.watchAdDescription', 'Regardez une courte vidéo publicitaire pour débloquer l\'accès temporairement.')}
              </p>
              <ActionButton
                $variant="secondary"
                onClick={handleWatchAd}
                disabled={isWatchingAd || isProcessingPayment}
              >
                {isWatchingAd ? (
                  <>
                    <FiLoader style={{ animation: `${spin} 1s linear infinite` }} />
                    {t('monetization.modal.loading', 'Chargement...')}
                  </>
                ) : (
                  <>
                    <FiGift />
                    {t('monetization.modal.watchAdButton', 'Regarder la vidéo')}
                  </>
                )}
              </ActionButton>

              <TimerInfo>
                <div className="timer-text">
                  <FiClock />
                  {t('monetization.modal.unlockDuration', 'Accès pendant')}
                  <span className="timer-duration">
                    {getUnlockDuration()} {t('monetization.modal.hours', 'heure(s)')}
                  </span>
                </div>
              </TimerInfo>
            </OptionCard>
          )}

          <OptionCard $isPrimary>
            <h3>
              <FiZap />
              {t('monetization.modal.upgrade', 'Passer à Premium')}
            </h3>
            <p>
              {t('monetization.modal.upgradeDescription', 'Accès illimité à toutes les fonctionnalités, sans publicité.')}
            </p>
            <ActionButton
              $variant="primary"
              onClick={handleUpgrade}
              disabled={isProcessingPayment}
            >
              {isProcessingPayment ? (
                <>
                  <FiLoader style={{ animation: `${spin} 1s linear infinite` }} />
                  {t('monetization.modal.processing', 'Traitement...')}
                </>
              ) : (
                <>
                  <FiStar />
                  {t('monetization.modal.upgradeButton', 'Premium {{price}}{{currency}}/mois', {
                    price: premiumPrice,
                    currency: premiumCurrency
                  })}
                </>
              )}
            </ActionButton>
          </OptionCard>
        </OptionsContainer>
      </ModalContent>
    </ReusableModal>
  );
};

export default MonetizationModal;
