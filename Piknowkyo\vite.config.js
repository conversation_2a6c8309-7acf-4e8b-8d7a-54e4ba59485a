import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
// https://vitejs.dev/config/
export default defineConfig({
    plugins: [react()],
    server: {
        port: 3000,
        open: true,
        headers: {
            'Cross-Origin-Embedder-Policy': 'credentialless',
            'Cross-Origin-Opener-Policy': 'same-origin',
        },
        fs: {
            allow: ['..'],
        },
    },
    build: {
        outDir: 'dist',
        sourcemap: true,
        rollupOptions: {
            external: [],
        },
    },
    define: {
        global: 'globalThis',
    },
    optimizeDeps: {
        include: [],
        exclude: [],
    },
    worker: {
        format: 'es',
    },
});
