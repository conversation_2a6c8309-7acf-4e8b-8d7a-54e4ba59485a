import styled from 'styled-components';

interface CardProps {
  $elevated?: boolean;
  $noPadding?: boolean;
  $gradient?: boolean;
}

const Card = styled.div.withConfig({ shouldForwardProp: (prop) => !['$elevated', '$noPadding', '$gradient'].includes(prop) })<CardProps>`
  background: ${({ theme, $gradient }) =>
    $gradient
      ? `linear-gradient(135deg, ${theme.primary}, ${theme.accent})`
      : theme.surface
  };
  color: ${({ theme, $gradient }) => $gradient ? '#ffffff' : theme.text};
  border-radius: 12px;
  box-shadow: ${({ theme, $elevated }) =>
    $elevated
      ? '0 8px 16px rgba(0, 0, 0, 0.1)'
      : '0 4px 6px rgba(0, 0, 0, 0.05)'
  };
  margin: 1rem 0;
  padding: ${({ $noPadding }) => $noPadding ? '0' : '1.5rem'};
  transition: all 0.3s ease;

  &:hover {
    box-shadow: ${({ theme, $elevated }) =>
      $elevated
        ? '0 12px 20px rgba(0, 0, 0, 0.15)'
        : '0 6px 12px rgba(0, 0, 0, 0.08)'
    };
    transform: ${({ $elevated }) => $elevated ? 'translateY(-5px)' : 'none'};
  }
`;

export default Card;
