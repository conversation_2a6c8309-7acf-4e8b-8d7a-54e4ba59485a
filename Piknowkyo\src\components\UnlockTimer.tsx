import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiClock, FiPlay } from 'react-icons/fi';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { removeExpiredUnlocks } from '../store/slices/monetizationSlice';
import { MonetizationService } from '../services/monetizationService';

const TimerContainer = styled.div<{ $isExpiring?: boolean }>`
  background: ${({ theme, $isExpiring }) => $isExpiring ? theme.warning || '#ff9800' : theme.primary};
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  animation: ${({ $isExpiring }) => $isExpiring ? 'pulse 1s infinite' : 'none'};
  
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
  }
`;

const TimeDisplay = styled.span`
  font-weight: 600;
  min-width: 60px;
  text-align: center;
`;

interface UnlockTimerProps {
  type: 'session' | 'game';
  sessionId?: string;
  onExpired?: () => void;
  showExtendOption?: boolean;
  onExtend?: () => void;
}

const UnlockTimer: React.FC<UnlockTimerProps> = ({
  type,
  sessionId,
  onExpired,
  showExtendOption = false,
  onExtend,
}) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { temporaryUnlocks } = useAppSelector(state => state.monetization);
  
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [isExpiring, setIsExpiring] = useState(false);

  // Trouver l'unlock correspondant
  const relevantUnlock = temporaryUnlocks.find(unlock => 
    unlock.type === type && 
    MonetizationService.isUnlockValid(unlock) &&
    (type === 'game' || unlock.sessionId === sessionId || !unlock.sessionId)
  );

  useEffect(() => {
    if (!relevantUnlock) {
      setTimeRemaining(0);
      return;
    }

    const updateTimer = () => {
      const remaining = MonetizationService.getUnlockTimeRemaining(relevantUnlock);
      setTimeRemaining(remaining);
      
      // Marquer comme expirant si moins de 5 minutes
      setIsExpiring(remaining <= 5 && remaining > 0);
      
      if (remaining <= 0) {
        dispatch(removeExpiredUnlocks());
        if (onExpired) {
          onExpired();
        }
      }
    };

    // Mise à jour immédiate
    updateTimer();
    
    // Mise à jour toutes les minutes
    const interval = setInterval(updateTimer, 60000);
    
    return () => clearInterval(interval);
  }, [relevantUnlock, dispatch, onExpired]);

  const formatTime = (minutes: number): string => {
    if (minutes <= 0) return '0m';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  if (!relevantUnlock || timeRemaining <= 0) {
    return null;
  }

  return (
    <TimerContainer $isExpiring={isExpiring}>
      <FiClock />
      <span>
        {type === 'session' 
          ? t('unlock.sessionTimeRemaining', 'Accès:')
          : t('unlock.gameTimeRemaining', 'Jeu:')
        }
      </span>
      <TimeDisplay>{formatTime(timeRemaining)}</TimeDisplay>
      
      {showExtendOption && isExpiring && onExtend && (
        <button
          onClick={onExtend}
          style={{
            background: 'rgba(255,255,255,0.2)',
            border: 'none',
            color: 'white',
            padding: '0.25rem 0.5rem',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '0.8rem',
            marginLeft: '0.5rem',
          }}
        >
          <FiPlay size={12} />
          {t('unlock.extend', 'Prolonger')}
        </button>
      )}
    </TimerContainer>
  );
};

export default UnlockTimer;
