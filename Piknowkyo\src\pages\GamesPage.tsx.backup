// src/pages/GamesPage.tsx

import React, { useState, useEffect, useCallback, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiPlay, FiInfo, FiAward, FiStar, FiClock, FiGrid, FiZap, FiLock } from 'react-icons/fi';
import { GameInfo, GameProps, SavedGameState } from '../games/common/models';
import { loadGameState, submitScoreToLeaderboard, getPersonalBestScore } from '../games/common/gameUtils';
import ReusableModal from '../components/ReusableModal';
import MonetizationModal from '../components/MonetizationModal';
import UnlockTimer from '../components/UnlockTimer';
import { useAppSelector } from '../store/hooks';
import { MonetizationService } from '../services/monetizationService';

// CORRECTION : Déplacer tous les imports en haut du fichier
import ZenTetrisGame from '../games/zen-tetris/GameComponent'; // Exemple: Zen Tetris
// import MemoryChallengeGame from '../games/memory-challenge/GameComponent'; // Autre jeu

// IMPORTANT: Vous devez implémenter votre AuthContext et fournir un userId
// Pour l'exemple, j'ai inclus un placeholder simple.
// Si vous n'avez pas de système d'authentification, userId sera "guest"
const useAuth = () => ({ userId: "guest_user_123" }); // Cette déclaration doit rester après tous les imports

// --- Styled Components spécifiques à GamesPage ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageTitle = styled.h1`
  font-size: 2.2rem;
  color: ${({ theme }) => theme.primary};
  text-align: center;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
`;

const GameGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const GameCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid ${({ theme }) => theme.border};

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    border-color: ${({ theme }) => theme.primary};
  }

  @media (max-width: 768px) {
    &:hover {
      transform: none;
    }
  }
`;

const GameThumbnail = styled.div<{ $imageUrl: string }>`
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, ${({ theme }) => theme.primary}20, ${({ theme }) => theme.secondary}20),
              url(${({ $imageUrl }) => $imageUrl});
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, ${({ theme }) => theme.primary}15, transparent);
  }

  @media (max-width: 768px) {
    height: 160px;
  }
`;

const GameIcon = styled.div`
  position: relative;
  z-index: 1;
  font-size: 3rem;
  color: ${({ theme }) => theme.primary};
  background: ${({ theme }) => theme.surface};
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

const CardContent = styled.div`
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const CardTitle = styled.h3`
  font-size: 1.5rem;
  color: ${({ theme }) => theme.text};
  margin: 0 0 0.75rem 0;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 1.3rem;
  }
`;

const CardDescription = styled.p`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textSecondary};
  line-height: 1.4;
  flex-grow: 1;
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1rem;
  position: relative;
`;

const PlayButton = styled.button`
  background: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight};
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: ${({ theme }) => theme.secondary};
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }

  @media (max-width: 768px) {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
`;

const InfoButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.primary};
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
  position: absolute;
  right: 0;

  &:hover {
    background-color: ${({ theme }) => theme.surfaceAlt || theme.surface};
    transform: scale(1.1);
  }
`;

const GameStats = styled.div`
  display: flex;
  flex-direction: column;
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  gap: 0.2rem;
  margin-top: 0.5rem;
  span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
  }
`;

// --- Liste des jeux disponibles ---
const GAMES_LIST: GameInfo[] = [
  {
    id: "zen-tetris",
    titleKey: "games.zenTetris.title",
    descriptionKey: "games.zenTetris.description",
    thumbnailUrl: "/assets/images/games/zen_tetris_thumbnail.jpg",
    component: ZenTetrisGame,
    maxLevels: 100,
    estimatedDurationMinutes: 10,
    tags: ["concentration", "memory", "relaxation", "reflexes"],
    icon: FiGrid
  },
  // {
  //   id: "memory-challenge",
  //   titleKey: "games.memoryChallenge.title",
  //   descriptionKey: "games.memoryChallenge.description",
  //   thumbnailUrl: "/assets/images/games/memory_challenge_thumbnail.jpg",
  //   component: MemoryChallengeGame,
  //   maxLevels: 100,
  //   estimatedDurationMinutes: 5,
  //   tags: ["mémoire", "logique"]
  // },
  // ... ajoutez d'autres jeux ici
];

const GamesPage: React.FC = () => {
  const { t } = useTranslation();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const { userId } = useAuth(); // Obtenez l'ID utilisateur via votre contexte d'authentification

  // Redux state pour la monétisation
  const { currentPlan, temporaryUnlocks } = useAppSelector(state => state.monetization);

  const [activeGameInfo, setActiveGameInfo] = useState<GameInfo | null>(null);
  const [initialGameLoadState, setInitialGameLoadState] = useState<SavedGameState | null>(null);

  const [showInfoModal, setShowInfoModal] = useState(false);
  const [selectedGameForInfo, setSelectedGameForInfo] = useState<GameInfo | null>(null);
  const [showMonetizationModal, setShowMonetizationModal] = useState(false);
  const [currentPersonalBest, setCurrentPersonalBest] = useState<number>(0);

  const [showGameOverModal, setShowGameOverModal] = useState(false);
  const [lastGameResults, setLastGameResults] = useState<{ score: number; level: number; time: number } | null>(null);

  // Charger la meilleure progression personnelle quand la modale info s'ouvre
  useEffect(() => {
    if (showInfoModal && selectedGameForInfo && userId) {
      setCurrentPersonalBest(getPersonalBestScore(selectedGameForInfo.id, userId));
    }
  }, [showInfoModal, selectedGameForInfo, userId]);


  const handlePlayGame = useCallback((game: GameInfo) => {
    // Vérifier les restrictions de monétisation
    const canAccess = MonetizationService.canAccessGame(game.id, currentPlan, temporaryUnlocks);

    if (!canAccess) {
      setShowMonetizationModal(true);
      return;
    }

    // Vérifier si une partie est sauvegardée
    const savedState = userId ? loadGameState(game.id, userId) : null;
    if (savedState) {
      setInitialGameLoadState(savedState);
    } else {
      setInitialGameLoadState(null); // Pas de sauvegarde, commencer frais
    }
    setActiveGameInfo(game); // Lance le jeu en affichant son composant
  }, [userId, currentPlan, temporaryUnlocks]);

  const handleGameEnd = useCallback((score: number, levelReached: number, finalTimeSeconds: number) => {
    // Le jeu est terminé, soumettre le score sera géré par le jeu lui-même maintenant
    // Le jeu appelera submitScoreToLeaderboard directement si la partie est finie.
    // Ici, on gère juste l'affichage post-game.
    if (activeGameInfo) {
      setLastGameResults({ score, level: levelReached, time: finalTimeSeconds });
      setShowGameOverModal(true);
    }
    setActiveGameInfo(null); // Revenir à la liste des jeux
  }, [activeGameInfo]); // userId n'est plus une dépendance car submitScore est appelé par le jeu

  const handleGameQuit = useCallback(() => {
    setActiveGameInfo(null); // Revenir à la liste des jeux
    setInitialGameLoadState(null); // Effacer l'état initial chargé pour la prochaine fois
  }, []);

  const handleOpenInfoModal = (game: GameInfo) => {
    setSelectedGameForInfo(game);
    setShowInfoModal(true);
  };

  const handleCloseInfoModal = () => {
    setShowInfoModal(false);
    setSelectedGameForInfo(null);
  };

  const handleCloseGameOverModal = () => {
      setShowGameOverModal(false);
      setLastGameResults(null);
  }

  // Rendu conditionnel du jeu ou de la liste des jeux
  if (activeGameInfo) {
    const GameComponent = activeGameInfo.component;
    return (
      <GameComponent
        userId={userId || "guest"} // Utiliser "guest" si pas authentifié, mais la sauvegarde ne fonctionnera pas sans userId réel
        onGameEnd={handleGameEnd}
        onGameQuit={handleGameQuit}
        onPauseChange={() => { /* Vous pouvez mettre à jour un état global ici si besoin */ }}
        initialGameState={initialGameLoadState} // 'initialGameLoadState' peut être null, ce qui est maintenant accepté par GameProps
      />
    );
  }

  return (
    <PageContainer>
      <PageTitle>{t('games.title', 'Mini-Jeux de Développement Personnel')}</PageTitle>
      <p style={{textAlign: 'center', color: theme.textMuted}}>{t('games.intro', 'Testez et améliorez vos compétences avec nos mini-jeux amusants et stimulants.')}</p>

      {/* Afficher le timer d'unlock si actif */}
      {currentPlan === 'free' && temporaryUnlocks.some(unlock => unlock.type === 'game' && unlock.expiresAt > Date.now()) && (
        <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '1rem' }}>
          <UnlockTimer
            type="game"
            onExpired={() => {
              // Optionnel: afficher une notification ou rediriger
              alert(t('games.unlockExpired', 'Votre accès temporaire aux jeux a expiré.'));
            }}
            showExtendOption={true}
            onExtend={() => setShowMonetizationModal(true)}
          />
        </div>
      )}

      <GameGrid>
        {GAMES_LIST.map((game) => {
          const personalBest = userId ? getPersonalBestScore(game.id, userId) : 0;
          const savedGame = userId ? loadGameState(game.id, userId) : null;
          const canAccess = MonetizationService.canAccessGame(game.id, currentPlan, temporaryUnlocks);

          return (
            <GameCard key={game.id}>
              <GameThumbnail $imageUrl={game.thumbnailUrl}>
                <GameIcon>
                  {game.icon && <game.icon />}
                </GameIcon>
                {!canAccess && (
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'rgba(0, 0, 0, 0.7)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '2rem'
                  }}>
                    <FiLock />
                  </div>
                )}
              </GameThumbnail>
              <CardContent>
                <CardTitle>
                  {t(game.titleKey)}
                  {!canAccess && (
                    <span style={{
                      background: theme.primary,
                      color: 'white',
                      fontSize: '0.7rem',
                      padding: '0.2rem 0.5rem',
                      borderRadius: '12px',
                      fontWeight: 600,
                      marginLeft: '0.5rem'
                    }}>
                      <FiStar size={10} style={{ marginRight: '0.25rem' }} />
                      Premium
                    </span>
                  )}
                </CardTitle>
                <CardDescription>{t(game.descriptionKey)}</CardDescription>
                <GameStats>
                    {game.estimatedDurationMinutes && (
                        <span><FiClock /> {t('games.estimatedDuration', 'Durée estimée')}: {game.estimatedDurationMinutes} {t('units.minutes', 'min')}</span>
                    )}
                    {personalBest > 0 && (
                        <span><FiAward /> {t('games.personalBest', 'Record personnel')}: {personalBest} {t('units.points', 'pts')}</span>
                    )}
                    {savedGame && (
                        <span><FiStar style={{color: theme.accent}} /> {t('games.savedGameProgress', 'Partie sauvegardée')}: {savedGame.level}</span>
                    )}
                </GameStats>
              </CardContent>
              <CardFooter>
                <PlayButton
                  onClick={() => handlePlayGame(game)}
                  style={{
                    opacity: canAccess ? 1 : 0.6,
                    cursor: canAccess ? 'pointer' : 'not-allowed'
                  }}
                >
                  {!canAccess ? (
                    <><FiLock /> {t('games.premiumRequired', 'Premium requis')}</>
                  ) : (
                    <><FiPlay /> {savedGame ? t('games.continueGame', 'Continuer la partie') : t('games.newGame', 'Nouvelle partie')}</>
                  )}
                </PlayButton>
                <InfoButton onClick={() => handleOpenInfoModal(game)}>
                  <FiInfo />
                </InfoButton>
              </CardFooter>
            </GameCard>
          );
        })
      </GameGrid>

      {/* Modale d'informations sur le jeu */}
      {selectedGameForInfo && (
        <ReusableModal
          isOpen={showInfoModal}
          onClose={handleCloseInfoModal}
          title={t(selectedGameForInfo.titleKey)}
          titleIcon={<FiInfo />}
          footerContent={
            <PlayButton onClick={() => { handlePlayGame(selectedGameForInfo); handleCloseInfoModal(); }}>
              <FiPlay /> {loadGameState(selectedGameForInfo.id, userId || "guest") ? t('games.continueGame', 'Continuer la partie') : t('games.newGame', 'Nouvelle partie')}
            </PlayButton>
          }
        >
          <p>{t(selectedGameForInfo.descriptionKey)}</p>
          <p>{t('games.maxLevels', 'Ce jeu contient {{maxLevels}} niveaux de difficulté.', { maxLevels: selectedGameForInfo.maxLevels })}</p>
          {userId && (
              <p>{t('games.yourBestScore', 'Votre meilleur score sur ce jeu est de {{score}} points.', { score: currentPersonalBest })}</p>
          )}
          {selectedGameForInfo.tags && selectedGameForInfo.tags.length > 0 && (
              <p>{t('games.keywords', 'Mots-clés')} : {selectedGameForInfo.tags.join(', ')}</p>
          )}
        </ReusableModal>
      )}

      {/* Modale de fin de partie */}
      <ReusableModal
        isOpen={showGameOverModal}
        onClose={handleCloseGameOverModal}
        title={t('game.modal.gameOverTitle', 'Partie Terminée !')}
        titleIcon={<FiAward />} // Commentaire sur une seule ligne maintenant
        footerContent={
            <PlayButton onClick={handleCloseGameOverModal}>{t('actions.ok', 'OK')}</PlayButton>
        }
      >
        {lastGameResults && (
            <p>
                {t('games.gameOverSummary', 'Félicitations ! Votre score final est de {{score}} points et vous avez atteint le niveau {{level}} en {{time}} secondes.', {
                    score: lastGameResults.score,
                    level: lastGameResults.level,
                    time: lastGameResults.time
                })}
            </p>
        )}
      </ReusableModal>

      {/* Modal de monétisation */}
      <MonetizationModal
        isOpen={showMonetizationModal}
        onClose={() => setShowMonetizationModal(false)}
        type="game"
        onUpgrade={() => {
          // Rediriger vers la page de monétisation
          window.location.href = '/monetization';
        }}
      />

    </PageContainer>
  );
};

export default GamesPage;