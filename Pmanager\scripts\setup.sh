#!/bin/bash

echo "🚀 Configuration de Piknowkyo Admin"
echo "=================================="

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

echo "✅ Node.js et npm sont installés"

# Installer les dépendances
echo "📦 Installation des dépendances..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Erreur lors de l'installation des dépendances"
    exit 1
fi

echo "✅ Dépendances installées avec succès"

# Créer le fichier .env s'il n'existe pas
if [ ! -f .env ]; then
    echo "📝 Création du fichier .env..."
    cp .env.example .env
    echo "⚠️  Veuillez configurer vos clés Firebase dans le fichier .env"
else
    echo "ℹ️  Le fichier .env existe déjà"
fi

# Vérifier si Firebase CLI est installé
if ! command -v firebase &> /dev/null; then
    echo "⚠️  Firebase CLI n'est pas installé. Installation recommandée :"
    echo "   npm install -g firebase-tools"
else
    echo "✅ Firebase CLI est installé"
fi

echo ""
echo "🎉 Configuration terminée !"
echo ""
echo "📋 Prochaines étapes :"
echo "1. Configurer vos clés Firebase dans .env"
echo "2. Exécuter le script d'initialisation : node scripts/init-admin.js"
echo "3. Lancer l'application : npm run dev"
echo ""
echo "📚 Documentation complète dans README.md"
