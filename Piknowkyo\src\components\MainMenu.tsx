import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../ThemeProvider';
import styled from 'styled-components';
import { FiHome, FiSettings, FiUser, FiBook, FiBarChart2, FiAward, FiMessageSquare, FiDollarSign, FiX, FiSun, FiMoon } from 'react-icons/fi';
import { FaGamepad } from 'react-icons/fa';
import LanguageSwitcher from './LanguageSwitcher';

interface MainMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

const MenuOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: ${({ $isOpen }) => ($isOpen ? 1 : 0)};
  visibility: ${({ $isOpen }) => ($isOpen ? 'visible' : 'hidden')};
  transition: opacity 0.3s ease, visibility 0.3s ease;
`;

const MenuContainer = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background-color: ${({ theme }) => theme.surface};
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  transform: translateX(${({ $isOpen }) => ($isOpen ? '0' : '100%')});
  transition: transform 0.3s ease;
  padding: 2rem;
  overflow-y: auto;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: ${({ theme }) => theme.text};
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: ${({ theme }) => theme.primary};
    background: none;
    transform: none;
  }
`;

const MenuHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 2rem;

  img {
    height: 40px;
    margin-right: 1rem;
  }

  h2 {
    font-size: 1.5rem;
    margin: 0;
    color: ${({ theme }) => theme.primary};
  }
`;

const MenuList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const MenuItem = styled.li<{ $active: boolean }>`
  margin-bottom: 0.5rem;

  a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    color: ${({ theme, $active }) => ($active ? theme.primary : theme.text)};
    background-color: ${({ theme, $active }) => ($active ? `${theme.primary}10` : 'transparent')};
    font-weight: ${({ $active }) => ($active ? '500' : '400')};
    transition: all 0.2s ease;

    &:hover {
      background-color: ${({ theme }) => `${theme.primary}10`};
    }

    svg {
      margin-right: 1rem;
      font-size: 1.25rem;
    }
  }
`;

const MenuSection = styled.div`
  margin-bottom: 2rem;

  h3 {
    font-size: 0.875rem;
    text-transform: uppercase;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.5rem;
    padding-left: 1rem;
  }
`;

const ThemeToggle = styled.button`
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  background: none;
  border: none;
  color: ${({ theme }) => theme.text};
  font-size: 1rem;
  font-weight: 400;
  cursor: pointer;
  margin-top: 1rem;

  &:hover {
    background-color: ${({ theme }) => `${theme.primary}10`};
    color: ${({ theme }) => theme.text};
    transform: none;
  }

  svg {
    margin-right: 1rem;
    font-size: 1.25rem;
  }
`;

const MainMenu: React.FC<MainMenuProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const location = useLocation();
  const { darkMode, toggleTheme } = useTheme();

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <>
      <MenuOverlay $isOpen={isOpen} onClick={onClose} />
      <MenuContainer $isOpen={isOpen}>
        <CloseButton onClick={onClose}>
          <FiX />
        </CloseButton>

        <MenuHeader>
          <img src="/logo192.png" alt="Piknowkyo" />
          <h2>{t('app.name')}</h2>
        </MenuHeader>

        <MenuSection>
          <h3>{t('menu.navigation')}</h3>
          <MenuList>
            <MenuItem $active={isActive('/')}>
              <Link to="/" onClick={onClose}>
                <FiHome />
                {t('navigation.home')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/sessions')}>
              <Link to="/sessions" onClick={onClose}>
                <FiBook />
                {t('navigation.sessions')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/journal')}>
              <Link to="/journal" onClick={onClose}>
                <FiMessageSquare />
                {t('navigation.journal')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/stats')}>
              <Link to="/stats" onClick={onClose}>
                <FiBarChart2 />
                {t('navigation.stats')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/leaderboard')}>
              <Link to="/leaderboard" onClick={onClose}>
                <FiAward />
                {t('navigation.leaderboard')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/blog')}>
              <Link to="/blog" onClick={onClose}>
                <FiMessageSquare />
                {t('navigation.blog')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/games')}>
              <Link to="/games" onClick={onClose}>
                <FaGamepad />
                {t('navigation.games')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/about')}>
              <Link to="/about" onClick={onClose}>
                <FiBook />
                {t('navigation.about')}
              </Link>
            </MenuItem>
          </MenuList>
        </MenuSection>

        <MenuSection>
          <h3>{t('menu.account')}</h3>
          <MenuList>
            <MenuItem $active={isActive('/profile')}>
              <Link to="/profile" onClick={onClose}>
                <FiUser />
                {t('navigation.profile')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/monetization')}>
              <Link to="/monetization" onClick={onClose}>
                <FiDollarSign />
                {t('navigation.monetization')}
              </Link>
            </MenuItem>
            <MenuItem $active={isActive('/settings')}>
              <Link to="/settings" onClick={onClose}>
                <FiSettings />
                {t('navigation.settings')}
              </Link>
            </MenuItem>
          </MenuList>
        </MenuSection>

        <MenuSection>
          <LanguageSwitcher />
        </MenuSection>

        <ThemeToggle onClick={() => { toggleTheme(); }}>
          {darkMode ? <FiSun /> : <FiMoon />}
        </ThemeToggle>
      </MenuContainer>
    </>
  );
};

export default MainMenu;
