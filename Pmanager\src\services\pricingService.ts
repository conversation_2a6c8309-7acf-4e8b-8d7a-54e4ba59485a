import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  query,
  orderBy,
  where
} from 'firebase/firestore';
import { db } from '@/config/firebase';
import { SubscriptionPricing, UserSubscription } from '@/types';

class PricingService {
  private static instance: PricingService;

  static getInstance(): PricingService {
    if (!PricingService.instance) {
      PricingService.instance = new PricingService();
    }
    return PricingService.instance;
  }

  // Récupérer tous les plans de tarification
  async getAllPricingPlans(): Promise<SubscriptionPricing[]> {
    try {
      const q = query(
        collection(db, 'subscription_pricing'),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      } as SubscriptionPricing));
    } catch (error) {
      console.error('Erreur lors de la récupération des plans:', error);
      throw error;
    }
  }

  // Récupérer les plans actifs
  async getActivePricingPlans(): Promise<SubscriptionPricing[]> {
    try {
      const q = query(
        collection(db, 'subscription_pricing'),
        where('isActive', '==', true),
        orderBy('price', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      } as SubscriptionPricing));
    } catch (error) {
      console.error('Erreur lors de la récupération des plans actifs:', error);
      throw error;
    }
  }

  // Récupérer un plan spécifique
  async getPricingPlan(id: string): Promise<SubscriptionPricing | null> {
    try {
      const docRef = doc(db, 'subscription_pricing', id);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        return null;
      }

      const data = docSnap.data();
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      } as SubscriptionPricing;
    } catch (error) {
      console.error('Erreur lors de la récupération du plan:', error);
      throw error;
    }
  }

  // Créer un nouveau plan
  async createPricingPlan(plan: Omit<SubscriptionPricing, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const newPlan = {
        ...plan,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const docRef = await addDoc(collection(db, 'subscription_pricing'), newPlan);
      return docRef.id;
    } catch (error) {
      console.error('Erreur lors de la création du plan:', error);
      throw error;
    }
  }

  // Mettre à jour un plan
  async updatePricingPlan(id: string, updates: Partial<SubscriptionPricing>): Promise<void> {
    try {
      const docRef = doc(db, 'subscription_pricing', id);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date(),
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour du plan:', error);
      throw error;
    }
  }

  // Supprimer un plan
  async deletePricingPlan(id: string): Promise<void> {
    try {
      const docRef = doc(db, 'subscription_pricing', id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Erreur lors de la suppression du plan:', error);
      throw error;
    }
  }

  // Activer/Désactiver un plan
  async togglePlanStatus(id: string, isActive: boolean): Promise<void> {
    try {
      await this.updatePricingPlan(id, { isActive });
    } catch (error) {
      console.error('Erreur lors du changement de statut du plan:', error);
      throw error;
    }
  }
}

export default PricingService.getInstance();
