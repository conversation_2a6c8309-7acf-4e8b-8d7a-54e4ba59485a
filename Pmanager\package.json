{"name": "piknowkyo-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc -b && vite build", "test": "vitest", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build && firebase deploy"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "firebase": "^11.8.1", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-icons": "^5.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "redux-persist": "^6.0.0", "styled-components": "^6.1.18", "@mui/material": "^6.1.9", "@mui/icons-material": "^6.1.9", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/x-data-grid": "^7.22.2", "@mui/x-date-pickers": "^7.22.2", "date-fns": "^4.1.0", "recharts": "^2.13.3", "axios": "^1.7.9", "react-hook-form": "^7.54.2", "@hookform/resolvers": "^3.10.0", "yup": "^1.6.0", "react-hot-toast": "^2.4.1", "groq-sdk": "^0.22.0", "@google/generative-ai": "^0.21.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^22.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^2.1.8"}}