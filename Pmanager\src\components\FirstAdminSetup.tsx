import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/config/firebase';
import { AdminUser } from '@/types';

interface FirstAdminSetupProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const FirstAdminSetup: React.FC<FirstAdminSetupProps> = ({ open, onClose, onSuccess }) => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('AdminPiknowkyo2024!');
  const [displayName, setDisplayName] = useState('Super Administrateur');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleCreateAdmin = async () => {
    if (!email || !password || !displayName) {
      setError('Tous les champs sont requis');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Créer l'utilisateur dans Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      
      // Créer le document admin dans Firestore
      const adminUser: Omit<AdminUser, 'uid'> = {
        email,
        displayName,
        role: 'super_admin',
        permissions: [
          'manage_users',
          'manage_subscriptions',
          'manage_sessions',
          'manage_pricing',
          'view_analytics',
          'manage_content',
          'manage_ai_apis',
          'manage_acl'
        ],
        createdAt: new Date(),
        isActive: true,
      };

      await setDoc(doc(db, 'admin_users', userCredential.user.uid), adminUser);
      
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Erreur lors de la création de l\'admin:', error);
      
      if (error.code) {
        switch (error.code) {
          case 'auth/email-already-in-use':
            setError('Cette adresse email est déjà utilisée');
            break;
          case 'auth/weak-password':
            setError('Le mot de passe est trop faible');
            break;
          case 'auth/invalid-email':
            setError('Adresse email invalide');
            break;
          default:
            setError(error.message || 'Erreur lors de la création du compte');
        }
      } else {
        setError('Erreur inconnue');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Configuration du Premier Administrateur
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 1 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Aucun administrateur n'a été trouvé. Créez le premier compte administrateur pour accéder au système.
          </Alert>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TextField
            margin="normal"
            required
            fullWidth
            label="Adresse email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={loading}
          />

          <TextField
            margin="normal"
            required
            fullWidth
            label="Mot de passe"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={loading}
            helperText="Minimum 6 caractères"
          />

          <TextField
            margin="normal"
            required
            fullWidth
            label="Nom d'affichage"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            disabled={loading}
          />

          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Ce compte aura tous les privilèges d'administration. Vous pourrez créer d'autres administrateurs par la suite.
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Annuler
        </Button>
        <Button 
          onClick={handleCreateAdmin} 
          variant="contained"
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Créer l\'Administrateur'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FirstAdminSetup;
