{"navigation": {"home": "Accueil", "sessions": "Séances", "games": "<PERSON><PERSON>", "journal": "Journal", "stats": "Statistiques", "leaderboard": "Classement", "blog": "Blog", "profile": "Profil", "monetization": "Premium", "settings": "Paramètres", "about": "À Propos"}, "common": {"welcome": "Bienvenue sur PiKnowKyo", "ok": "OK", "cancel": "Annuler", "save": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "close": "<PERSON><PERSON><PERSON>", "back": "Retour", "next": "Suivant", "previous": "Précédent", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès"}, "home": {"title": "Bienvenue sur PiKnowKyo", "subtitle": "Votre compagnon de bien-être personnel. Découvrez des séances guidées, suivez votre progression et cultivez votre épanouissement intérieur.", "exploreButton": "Explorer les Séances", "welcomeText": "Commencez votre voyage vers un bien-être durable avec nos outils personnalisés et notre communauté bienveillante.", "quickAccess": "Accès Rapide", "learnMore": "En savoir plus sur PiKnowKyo"}, "sessions": {"title": "Séances", "meditation": "Méditations", "hypnosis": "Hypnose", "affirmations": "Affirmations", "custom": "<PERSON><PERSON>", "description": "Explorez toutes les séances : hypnose, méditation, affirmation, training, histoires, personnalisées.", "premium": "Premium", "watchAdToUnlock": "Regarder une vidéo pour débloquer", "premiumRequired": "Premium requis", "unlockThisSession": "Débloquer cette séance premium", "premiumOnlySession": "Séance Premium Exclusive", "sessionLocked": "<PERSON><PERSON><PERSON>", "watchAdCTA": "Regarder une vidéo", "upgradeCTA": "Passer à Premium", "searchPlaceholder": "Rechercher une séance...", "allTypes": "Tous types", "allDurations": "<PERSON>utes durées", "durationLabel": "<PERSON><PERSON><PERSON>", "filterBy": "Filtrer par", "filters": "Filtres", "sessionType": "Type de séance", "gridView": "Vue Grille", "listView": "<PERSON><PERSON>", "noResultsMatchCriteria": "Aucune séance ne correspond à vos critères.", "noSessionsAvailable": "Aucune session disponible pour le moment.", "duration": {"label": "<PERSON><PERSON><PERSON>", "under15": "Moins de 15 min", "15to30": "15-30 min", "over30": "Plus de 30 min"}}, "games": {"title": "Mini-Jeux de Développement Personnel", "intro": "Testez et améliorez vos compétences avec nos mini-jeux amusants et stimulants.", "zenTetris": {"title": "Zen Tetris", "description": "Une version relaxante du célèbre jeu de blocs. Améliorez votre concentration et votre gestion du stress.", "rules": "Placez les pièces qui tombent pour compléter des lignes horizontales. Plus vous éliminez de lignes d'un coup, plus vous gagnez de points. Le jeu accélère progressivement.", "controls": "Contr<PERSON>les tactiles", "tips": "Restez calme, planifiez vos mouvements et essayez de créer des combos pour maximiser votre score.", "touchTap": "Tap rapide : Rotation", "touchLeft": "Glisser gauche : <PERSON><PERSON><PERSON><PERSON> à gauche", "touchRight": "Glisser droite : <PERSON><PERSON><PERSON><PERSON> à droite", "touchDown": "Glisser bas : <PERSON><PERSON> douce", "touchButtons": "Boutons de contrôle en bas"}, "estimatedDuration": "<PERSON><PERSON><PERSON> estimée", "personalBest": "Record personnel", "savedGameProgress": "<PERSON><PERSON>", "maxLevels": "Ce jeu contient {{maxLevels}} niveaux de difficulté.", "yourBestScore": "Votre meilleur score sur ce jeu est de {{score}} points.", "moveLeft": "<PERSON>éplacer à gauche", "moveRight": "<PERSON><PERSON><PERSON><PERSON> à droite", "softDrop": "<PERSON><PERSON> douce", "rotate": "Rotation", "level": "Niveau", "lines": "<PERSON><PERSON><PERSON>", "nextPiece": "<PERSON><PERSON><PERSON>", "info": "Infos", "continueGame": "Continuer la partie", "newGame": "Nouvelle partie", "premiumRequired": "Premium requis", "unlockExpired": "Votre accès temporaire aux jeux a expiré", "unlockThisGame": "Débloquer ce jeu premium", "watchAdCTA": "Regarder une vidéo", "upgradeCTA": "Passer à Premium", "keywords": "Mots-clés", "gameOverSummary": "Félicitations ! Votre score final est de {{score}} points et vous avez atteint le niveau {{level}} en {{time}} secondes.", "gameInfo": "Informations sur le jeu", "gameRules": "<PERSON><PERSON><PERSON> du <PERSON>eu", "gameOver": "<PERSON><PERSON> termin<PERSON>", "finalScore": "Score final", "newRecord": "Nouveau record !", "playAgain": "Rejouer", "backToGames": "Retour aux jeux", "pause": "Pause", "resume": "Reprendre", "quit": "<PERSON><PERSON><PERSON>"}, "game": {"pauseButton": "Mettre en pause", "info": "Infos", "level": "Niveau", "lines": "<PERSON><PERSON><PERSON>", "score": "Score", "time": "Temps", "nextPiece": "<PERSON><PERSON><PERSON>", "moveLeft": "<PERSON>éplacer à gauche", "moveRight": "<PERSON><PERSON><PERSON><PERSON> à droite", "rotate": "Rotation", "softDrop": "<PERSON><PERSON> douce", "modal": {"rulesTitle": "<PERSON><PERSON><PERSON>", "pausedTitle": "<PERSON><PERSON> en Pause", "gameOverTitle": "Partie Terminée !", "pausedMessage": "Votre partie est en pause. Reprenez quand vous êtes prêt.", "gameOverMessage": "Bien joué ! Votre score final est de {{score}} et vous avez atteint le niveau {{level}}.", "return": "Retour", "restart": "<PERSON><PERSON><PERSON><PERSON>", "resume": "Reprendre", "start": "Commencer"}, "controls": {"keyboard": "<PERSON><PERSON><PERSON>", "touch": "Tactile"}, "zenTetris": {"rules1": "Empilez les blocs pour former des lignes complètes et marquez des points. La vitesse augmente avec les niveaux !", "rules2": "Contrôles :", "ruleMoveLeft": "<PERSON>éplacer à gauche", "ruleMoveRight": "<PERSON><PERSON><PERSON><PERSON> à droite", "ruleSoftDrop": "Chute douce (soft drop)", "ruleRotate": "Rotation", "rulePause": "Pause"}}, "journal": {"title": "Journal de Suivi", "description": "Retrouvez ici toutes vos notes personnelles, classées par séance. Réfléchissez à vos expériences et suivez votre progression.", "noNotesYet": "Votre journal est encore vide.", "startSessionPrompt": "Commencez une séance et prenez des notes pour voir vos réflexions ici.", "unknownSession": "Séance (ID: {{id}})", "notesPlural": "notes", "noteSingular": "note", "seeAllNotes": "Voir toutes les {{count}} notes...", "trackingJournal": "Journal de Suivi", "addEntry": "A<PERSON><PERSON>z votre réflexion sur cette séance..."}, "stats": {"title": "Vos Statistiques de Bien-être", "description": "<PERSON><PERSON>z votre parcours, célébrez vos progrès et découvrez vos tendances.", "sessionsFollowed": "<PERSON><PERSON><PERSON><PERSON>", "sessionsFollowedDesc": "Nombre de sessions uniques avec des notes.", "totalTime": "Temps Total en Séance", "totalTimeDesc": "Temps cumulé estimé.", "favoriteSession": "<PERSON><PERSON><PERSON>", "favoriteSessionDesc": "La plus notée.", "notesWritten": "Total Notes Écrites", "notesWrittenDesc": "Nombre de réflexions enregistrées.", "typesFollowed": "Répartition par Type de Séance", "timePerSession": "<PERSON><PERSON><PERSON> par S<PERSON>ance (Estimé)", "noTypesYet": "Aucun type de séance spécifique suivi pour le moment.", "noTimePerSession": "Aucune donnée de temps par séance disponible.", "timesPlural": "fois", "timesSingular": "fois", "notesPlural": "notes", "noteSingular": "note", "duration": {"min": "min", "h": "h"}}, "blog": {"title": "Journal Communautaire", "description": "Partagez vos expériences, découvertes et inspirations avec la communauté PiKnowKyo. Tous les messages sont anonymes.", "searchPlaceholder": "Rechercher des messages...", "allCategories": "Toutes catégories", "writeNewPost": "Écrire un nouveau message", "postPlaceholder": "Votre message (sera publié anonymement)...", "category": "<PERSON><PERSON><PERSON><PERSON>", "publishing": "Publication...", "publish": "Publier", "loginToPost": "Vous devez être connecté pour publier un message.", "noPostsYet": "Aucun message pour le moment dans cette catégorie ou correspondant à votre recherche.", "like": "<PERSON><PERSON>", "comments": "Commentaires", "addComment": "Ajouter un commentaire", "commentPlaceholder": "Votre commentaire (anonyme)...", "postComment": "Publier le commentaire", "noCommentsYet": "Aucun commentaire pour le moment. Soyez le premier à commenter !", "backToBlog": "Retour au blog", "postNotFound": "Message introuvable", "commentsSectionTitle": "Commentaires", "yourCommentPlaceholder": "Votre commentaire...", "sending": "Envoi...", "sendComment": "Envoyer", "loginToComment": "Connectez-vous pour ajouter un commentaire.", "sampleAuthor": "Auteur Anonyme", "samplePostContent": "Contenu détaillé du message. Ce message parle de l'importance de la pleine conscience dans notre quotidien stressant et comment de simples exercices peuvent apporter une grande paix intérieure.", "sampleCommenter1": "Commentateur1", "sampleCommenter2": "AutrePersonne", "sampleComment1": "Super message !", "sampleComment2": "<PERSON><PERSON><PERSON> in<PERSON>, merci du partage.", "anonymousUser": "Utilisateur Anonyme", "categories": {"général": "Général", "gratitude": "Gratitude", "défis": "<PERSON><PERSON><PERSON><PERSON>", "inspirations": "Inspirations", "questions": "Questions"}}, "about": {"title": "À propos", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, votre compagnon de croissance personnelle et de bien-être.", "philosophy": {"title": "Notre Philosophie : Le Voyage de Pi à Kyo", "pi": {"title": "Pi (π)", "description": "<PERSON>'infini, le mystère sacré de l'univers et l'harmonie fondamentale qui nous unit tous. C'est le point de départ, l'ouverture à l'inconnu."}, "know": {"title": "Know (<PERSON><PERSON><PERSON><PERSON>)", "description": "L'exploration, l'apprentissage structuré et la clarté mentale. C'est l'acquisition des outils et des compréhensions pour naviguer le chemin."}, "kyo": {"title": "<PERSON><PERSON> (教え)", "description": "L'enseignement, la sagesse incarnée, l'illumination et le partage altruiste de la lumière découverte. C'est l'aboutissement et le rayonnement."}, "conclusion": "PiKnowKyo est plus qu'une application, c'est une boussole pour votre croissance intérieure, inspirée par"}, "tools": {"title": "Nos Outils pour Votre Épanouissement", "description": "Nous offrons une gamme variée de séances et d'outils conçus pour vous accompagner sur votre chemin de croissance personnelle :", "hypnosis": "Hypnose Évolutive pour explorer votre subconscient et initier des changements profonds.", "meditation": "Méditations Guidées pour cultiver la pleine conscience, la paix intérieure et la résilience émotionnelle.", "affirmations": "Affirmations Positives pour reprogrammer vos pensées et renforcer votre confiance en vous.", "nlp": "Training PNL (Programmation Neuro-Linguistique) pour améliorer votre communication et atteindre vos objectifs.", "stories": "Histoires Métaphoriques pour stimuler votre imagination et faciliter l'intégration de nouvelles perspectives."}, "experience": {"title": "Une Expérience Holistique Conçue pour Vous", "audio": "Audio 100% configurable (musique, ambiance, voix, binaural).", "guidedPaths": "Parcours guidés et création de séances sur mesure.", "community": "Communauté bienveillante et classement anonyme (optionnel).", "blog": "Blog interne avec articles, conseils et ressources inspirantes.", "multilang": "Support multi-langue et thèmes clair/sombre personnalisables.", "notifications": "Notifications de motivation douces pour vous accompagner."}, "features": {"customizable": "Séances entièrement personnalisables selon vos besoins.", "journal": "Journal personnel pour suivre votre progression et vos réflexions.", "stats": "Statistiques détaillées pour visualiser votre évolution."}, "monetization": {"title": "Monétisation Éthique :", "description": "Nous proposons un essai gratuit, un abonnement optionnel pour un accès complet, des publicités minimales et non-intrusives (contournables avec l'abonnement), et la possibilité de dons pour soutenir notre mission."}, "community": {"title": "Rejoignez Notre Communauté", "description": "PiKnowKyo est conçu pour ceux qui valorisent la connaissance de soi, l'organisation de leurs pensées et leur productivité personnelle dans une optique de mieux-être. Que vous soyez étudiant, professionnel, chercheur, ou simplement un esprit curieux en quête d'harmonie, notre application est votre alliée.", "contact": "Pour toute question, suggestion ou si vous avez besoin d'assistance, n'hésitez pas à nous envoyer un courriel à :", "website": "Vous pouvez également visiter notre site web", "moreInfo": "pour plus d'informations"}}, "settings": {"title": "Paramètres", "audio": "Audio", "language": "<PERSON><PERSON>", "voice": "Voix", "autoVoice": "Voix automatique", "testVoice": "Tester la voix", "saveConfig": "Sauvegarder la configuration", "ttsSectionTitle": "<PERSON>ynth<PERSON>e vocale (TTS)", "ttsProvider": "Fournisseur TTS", "ttsTestText": "<PERSON>ci est un test de synthèse vocale.", "ttsTestError": "Erreur lors du test de la voix", "downloadingVoice": "Téléchargement de la voix...", "voiceDownloaded": "Voix téléchargée", "noVoiceForSelection": "Aucune voix disponible pour cette sélection", "noSpecificVoiceForLang": "Aucune voix spécifique pour cette langue. Voici toutes les voix disponibles:", "explanationsTitle": "Explications", "audioAssetsManagementTitle": "Gestion des fichiers audio", "audioAssetsInfo": "<PERSON><PERSON><PERSON> vos musiques et sons d'ambiance personnalisés", "goToAudioAssets": "<PERSON><PERSON><PERSON> les fichiers audio", "providerLabels": {"browser": "Navigateur"}, "ttsProviderInfo": {"browser": "Utilise les voix intégrées du navigateur"}, "modal": {"saveSuccessTitle": "Configuration sauvegardée", "saveSuccessMessage": "Vos paramètres ont été sauvegardés avec succès", "testErrorTitle": "Erreur de test"}}, "monetization": {"modal": {"sessionTitle": "Accéder à \"{{title}}\"", "sessionTitleGeneric": "Accéder à cette séance", "gameTitle": "Accéder aux jeux", "sessionDescription": "Cette séance fait partie de nos fonctionnalités premium. Choisissez une option ci-dessous pour y accéder.", "gameDescription": "Les jeux de pleine conscience sont réservés aux membres premium. Choisissez une option ci-dessous pour y accéder.", "watchAd": "Regarder une vidéo", "watchAdDescription": "Regardez une courte vidéo publicitaire pour débloquer l'accès temporairement.", "watchAdButton": "Regarder la vidéo", "upgrade": "Passer à Premium", "upgradeDescription": "Accès illimité à toutes les fonctionnalités, sans publicité.", "upgradeButton": "Premium {{price}}{{currency}}/mois", "unlockDuration": "Accès pendant", "hours": "heure(s)", "loading": "Chargement...", "processing": "Traitement..."}, "adSuccess": "Vidéo regardée avec succès ! Vous avez maintenant accès à cette fonctionnalité.", "adFailed": "Erreur lors du chargement de la vidéo. Veuillez réessayer.", "adError": "Une erreur est survenue. Veuillez réessayer."}, "sessionDetails": {"description": "Description", "expectedBenefits": "Bénéfices Attendus", "keywords": "Mots-clés", "startSession": "Commencer la Séance", "backToSessions": "Retour aux Séances", "audioConfigGlobal": "Configuration Audio de la Séance", "userReviews": "Avis des Utilisateurs", "yourNotes": "Votre Journal pour cette Séance", "previousNotes": "Notes précédentes", "dnd": {"label": "<PERSON><PERSON> (Application)", "permissionNeededInfo": "Activer demandera la permission pour les notifications afin d'optimiser ce mode.", "permissionDeniedWarning": "Permission de notification refusée. Le mode NPD de l'application est actif, mais les notifications système ne sont pas affectées."}}, "units": {"minutes": "min", "points": "pts", "seconds": "sec"}, "actions": {"back": "Retour", "backToBlog": "Retour au blog", "backToHome": "Retour à l'accueil", "backToSessionDetails": "Retour aux détails", "backToSessions": "Retour aux séances", "backToSettings": "Retour aux paramètres", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirm": "Confirmer la <PERSON>", "deleting": "Suppression...", "enterFullscreen": "Mode plein écran", "exitFullscreen": "<PERSON><PERSON><PERSON> le plein écran", "ok": "OK", "pause": "Pause", "play": "Lecture", "preview": "Prévisualiser", "restart": "Recommencer", "startSession": "Commencer la séance", "stopPreview": "<PERSON><PERSON><PERSON><PERSON>", "stopTest": "<PERSON><PERSON><PERSON><PERSON> le test", "testSound": "Tester le son", "testVoice": "Tester la voix", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "menu": {"navigation": "Navigation", "account": "<PERSON><PERSON><PERSON>"}, "notFound": {"message": "<PERSON><PERSON><PERSON><PERSON>, la page que vous recherchez n'existe pas.", "backHome": "Retour à l'accueil"}, "quiz": {"title": "Quiz", "description": "Sélectionnez un quiz pour commencer à tester vos connaissances !", "comingSoon": "Les quiz arrivent bient<PERSON>t ! Restez connectés."}, "history": {"title": "Historique", "description": "Consultez vos résultats et votre progression.", "comingSoon": "L'historique sera bientôt disponible."}, "categories": {"title": "Catégories", "description": "Choisissez une catégorie pour explorer les quiz associés.", "comingSoon": "Les catégories arrivent bi<PERSON><PERSON><PERSON> !"}, "languages": {"french": "Français", "english": "English", "spanish": "Español"}, "notifications": {"notSupported": "Ce navigateur ne supporte pas les notifications."}, "pseudoGenerator": {"adjectives": {"light": "<PERSON><PERSON><PERSON>", "wind": "Vent", "ocean": "<PERSON><PERSON><PERSON>", "mountain": "<PERSON><PERSON><PERSON>", "star": "<PERSON><PERSON><PERSON>", "forest": "<PERSON><PERSON><PERSON>", "river": "Rivière", "sun": "<PERSON><PERSON>", "moon": "<PERSON><PERSON>", "aurora": "Au<PERSON>re"}, "nouns": {"serene": "<PERSON><PERSON><PERSON>", "calm": "Calme", "wise": "Sage", "peaceful": "Paisible", "clairvoyant": "Clairvoyante", "harmonious": "<PERSON><PERSON><PERSON><PERSON>", "awakened": "<PERSON><PERSON><PERSON><PERSON>", "free": "Libre", "creative": "Créative", "intuitive": "Intuitive"}}, "app": {"name": "PiKnowKyo"}, "preferences": {"language": {"question": "Choisissez votre langue préférée"}, "notifications": {"question": "Souh<PERSON>ez-vous recevoir des notifications de motivation ?"}, "premium": {"question": "Voulez-vous tester les fonctionnalités premium gratuitement (avec pubs non intrusives) ?"}, "yes": "O<PERSON>", "no": "Non", "thanks": "<PERSON><PERSON><PERSON> !", "validate": "Valider mes préférences"}, "questionnaire": {"goal": {"question": "Quel est votre objectif principal ?", "relaxation": "Détente", "confidence": "Confiance en soi", "stress": "Gestion du stress", "spirituality": "Spiritualité", "other": "<PERSON><PERSON>"}, "experience": {"question": "<PERSON><PERSON>-vous déjà pratiqué l'hypnose ou la méditation ?", "never": "<PERSON><PERSON>", "sometimes": "<PERSON><PERSON><PERSON>", "regularly": "Régulièrement"}, "audio": {"question": "<PERSON><PERSON><PERSON><PERSON><PERSON>-vous une séance avec musique, sons naturels, ou silence ?", "music": "Musique", "nature": "Sons naturels", "silence": "Silence"}, "thanks": "<PERSON><PERSON><PERSON> !", "viewSuggestions": "Voir mes suggestions"}, "notificationTest": {"heading": "Test de notifications", "platform": "Plateforme actuelle :", "status": "Statut des notifications web :", "title": "Test de notification", "body": "Ceci est un test de notification depuis PiKnowKyo", "sendButton": "Envoyer une notification de test"}, "reduxExample": {"loading": "Chargement des sessions...", "title": "Exemple Redux - Sessions", "reduxState": "État Redux :"}, "audioAssets": {"title": "Gestion des fichiers audio", "musicTitle": "Musiques", "ambientTitle": "Sons d'ambiance", "noMusics": "Aucune musique disponible", "noAmbiants": "Aucun son d'ambiance disponible", "selectFile": "Sélectionner un fichier", "changeFile": "Changer le <PERSON>er", "uploadMusicPrompt": "Téléverser une nouvelle musique", "uploadAmbientPrompt": "<PERSON><PERSON><PERSON><PERSON>r un nouveau son d'ambiance", "uploading": "Téléversement...", "uploadSuccess": "Fichier {{fileName}} téléversé avec succès !", "uploadError": "Erreur lors du téléversement", "previewError": "Impossible de lire l'aperçu audio", "cannotDeleteDefault": "Les fichiers par défaut ne peuvent pas être supprimés", "confirmDeleteTitle": "Confirmer la <PERSON>", "confirmDeleteMessage": "Êtes-vous sûr de vouloir supprimer ce fichier ?", "deleteSuccess": "Fichier supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de <PERSON>"}, "audioConfig": {"webAudioNotSupported": "Web Audio API non supportée par votre navigateur.", "musicTitle": "Musique de Fond", "musicTooltip": "Choisissez une musique d'ambiance pour accompagner votre séance. Vous pouvez ajuster le volume.", "ambientTitle": "Sons d'Ambiance", "ambientTooltip": "Ajoutez des sons naturels ou d'ambiance pour créer l'atmosphère parfaite.", "binauralBeats": "Sons Binauraux / Isochrones", "binauralTooltip": "G<PERSON><PERSON>rez des sons pour influencer les ondes cérébrales. Nécessite un casque pour l'effet binaural optimal.", "ttsTitle": "Synthèse Vocale", "ttsTooltip": "Ajustez le volume de la voix du guide. Le type de voix et la langue sont gérés dans les paramètres généraux de l'application.", "musicSound": "Musique :", "ambientSound": "Ambiance sonore :", "volume": "Volume", "baseFrequency": "Fréquence de base (Hz)", "baseFreqPresets": "Préréglages Fréq. de Base :", "beatFrequency": "Battement (Hz)", "brainwavePresets": "Préréglages Ondes Cérébrales (Battement) :", "selectPreset": "-- <PERSON><PERSON> un préréglage --", "selectState": "-- Choisir un état --", "targetFrequencyInfo": "Oreille G: {{leftEar}} Hz, Oreille D: {{rightEar}} Hz", "headphonesRequired": "Nécessite un casque pour l'effet binaural", "ambientPremium": "Sons Ambiants Premium", "ambientPremiumDesc": "Sons naturels pour une immersion totale", "binauralPremium": "Sons Binauraux Premium", "binauralPremiumDesc": "Battements binauraux pour la relaxation profonde", "upgradeToPremium": "Passer à Premium"}, "errors": {"missingPostId": "ID du message manquant.", "postNotFound": "Message non trouvé.", "cantLoadPost": "Impossible de charger le message.", "cantLoadComments": "Impossible de charger les commentaires.", "cantAddComment": "<PERSON><PERSON>ur lors de l'ajout du commentaire.", "userNotAuthenticated": "Vous devez être connecté pour effectuer cette action.", "manageSubscriptionError": "Impossible d'accéder à la gestion de votre abonnement.", "paymentError": "Erreur lors du traitement du paiement.", "cantAddPost": "Erreur lors de la publication du message.", "cantLoadSessions": "Impossible de charger les données des sessions."}, "player": {"sessionEnded": "Fin de la séance.", "readyToStart": "Prêt à commencer...", "audioSettings": "Volumes", "volumeControls": "Réglages des Volumes", "music": "Musique", "ambient": "Ambiance", "voice": "Voix", "binaural": "Battements binauraux", "sessionExpired": "Votre accès temporaire à cette séance a expiré.", "ambientLocked": "Sons d'ambiance", "binauralLocked": "Sons binauraux", "ambientDescription": "Sons naturels pour une immersion totale", "binauralDescription": "Battements binauraux pour la relaxation profonde", "clickToUpgrade": "Cliquer pour passer à Premium"}, "test": {"newSongTitle": "Titre de la nouvelle chanson", "addNewSong": "Ajouter une nouvelle chanson"}, "sync": {"offline": "<PERSON><PERSON> ligne", "syncing": "Synchronisation...", "error": "Erreur de sync ({{count}})", "pending": "{{count}} en attente", "synchronized": "Synchronisé", "syncedMinutesAgo": "Sync il y a {{minutes}}min", "syncedHoursAgo": "Sync il y a {{hours}}h", "online": "En ligne", "clickToSync": "Cliquer pour synchroniser"}, "loading": {"user": "Chargement des informations utilisateur...", "profile": "Chargement du profil...", "blog": "Chargement du blog...", "comments": "Chargement des commentaires...", "post": "Chargement du message...", "leaderboard": "Chargement du classement...", "stats": "Chargement des statistiques..."}, "plans": {"free": {"title": "Plan G<PERSON>uit", "price": "0$", "currentPlan": "Votre Plan Actuel", "switchToFree": "Passer au plan Gratuit"}, "premium": {"title": "Piknowkyo Premium", "billedMonthly": "Facturé mensuellement, annulez à tout moment.", "manageSub": "Gérer l'Abonnement", "subscribe": "Passer à Premium"}, "billing": {"month": "mois"}}, "features": {"free": {"baseMeditations": "Accès aux méditations et histoires de base", "backgroundMusic": "Musique de fond et voix TTS basiques", "stats": "Statistiques de progression", "blog": "Accès au blog communautaire", "leaderboard": "Participation au leaderboard anonyme"}, "premium": {"allSessions": "Accès illimité à TOUTES les séances (hypnose, PNL, etc.)", "ambientSounds": "Sons d'ambiance et binauraux avancés", "customSessions": "Création de séances personnalisées", "games": "Accès aux mini-jeux de pleine conscience", "journal": "Journal de suivi détaillé", "motivationNotifs": "Notifications de motivation personnalisées", "calendar": "Calendrier et programmes personnalisés (à venir)", "customAudio": "Possibilité d'utiliser vos propres sons et musiques", "noAds": "Expérience sans publicité", "prioritySupport": "Support prioritaire"}}, "legal": {"privacy": "Politique de confidentialité", "terms": "Te<PERSON><PERSON> et conditions"}, "profile": {"title": "Mon Profil", "notConnectedTitle": "<PERSON><PERSON>", "pleaseLogin": "Veuillez vous connecter pour accéder à votre profil.", "publicPseudo": "Pseudo public", "regeneratePseudo": "Générer un nouveau pseudo", "premiumMember": "Membre Premium", "manageSubscription": "<PERSON><PERSON>rer l'abonnement", "upgradeToPremium": "Passer à Premium", "preferencesTitle": "Préférences", "appLanguage": "Langue de l'application", "grammaticalGenderLabel": "Comment p<PERSON><PERSON><PERSON><PERSON>-vous que l'on s'adresse à vous dans les scripts ?", "grammaticalGenderInfo": "Cela nous aidera à adapter certains textes pour une expérience plus personnalisée.", "quickActions": "Actions rapides", "goToSettings": "Paramètres avancés", "viewStats": "Voir mes statistiques", "accountActionsTitle": "Gestion du Compte", "logout": "Déconnexion", "deleteAccount": "Supprimer mon compte", "deleteConfirmTitle": "Confirmer la Suppression", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer votre compte ? Toutes vos données, y compris votre progression et vos notes de journal, seront définitivement effacées. Cette action est irréversible.", "accountDeletedSuccess": "Votre compte et toutes vos données ont été supprimés.", "stats": {"sessionsCompleted": "Séances complétées", "daysStreak": "Jours consécutifs", "totalMinutes": "Minutes totales"}}, "gender": {"masculine": "Au masculin", "feminine": "Au féminin"}, "leaderboard": {"title": "Classement des Explorateurs", "description": "Découvrez votre position parmi les membres actifs de la communauté PiKnowKyo. Le classement est basé sur l'engagement et la progression (anonymisé pour la confidentialité).", "noData": "Le classement n'est pas encore disponible ou est en cours de calcul. Revenez bientôt !", "pseudo": "P<PERSON><PERSON> (Anonyme)", "score": "Score", "you": "Vous", "privacyNote": "Votre vie privée est importante. Tous les pseudos sont anonymisés pour protéger votre identité. Votre participation au classement est facultative et peut être gérée dans vos paramètres de profil."}, "unlock": {"sessionTimeRemaining": "Accès:", "gameTimeRemaining": "Jeu:", "extend": "Prolonger"}}