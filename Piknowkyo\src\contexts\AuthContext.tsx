import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import {
  User,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  GoogleAuthProvider,
  signInWithPopup,
  signInAnonymously
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '../firebase';

// Types pour l'abonnement
export type SubscriptionPlan = 'free' | 'premium';
export type SubscriptionStatus = 'active' | 'expired' | 'trial';

export interface UserSubscription {
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  startDate: Date;
  endDate?: Date;
  trialEndDate?: Date;
  isTrialActive: boolean;
}

export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  isAnonymous: boolean;
  subscription: UserSubscription;
  createdAt: Date;
  lastLoginAt: Date;
  preferences: {
    language: string;
    theme: string;
    notifications: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName?: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInAsGuest: () => Promise<void>;
  signOut: () => Promise<void>;
  updateUserProfile: (updates: Partial<UserProfile>) => Promise<void>;
  hasActivePremium: () => boolean;
  getRemainingTrialDays: () => number;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Créer un profil utilisateur par défaut
  const createDefaultUserProfile = (firebaseUser: User): UserProfile => {
    const now = new Date();
    const trialEndDate = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000); // 14 jours

    return {
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      isAnonymous: firebaseUser.isAnonymous,
      subscription: {
        plan: 'premium', // Commence en premium pour l'essai
        status: 'trial',
        startDate: now,
        trialEndDate,
        isTrialActive: true
      },
      createdAt: now,
      lastLoginAt: now,
      preferences: {
        language: 'fr',
        theme: 'light',
        notifications: true
      }
    };
  };

  // Vérifier et mettre à jour le statut de l'abonnement
  const checkAndUpdateSubscriptionStatus = (profile: UserProfile): UserProfile => {
    const now = new Date();
    const updatedProfile = { ...profile };

    if (profile.subscription.isTrialActive && profile.subscription.trialEndDate) {
      if (now > profile.subscription.trialEndDate) {
        // L'essai a expiré, passer au forfait gratuit
        updatedProfile.subscription = {
          ...profile.subscription,
          plan: 'free',
          status: 'active',
          isTrialActive: false
        };
      }
    }

    return updatedProfile;
  };

  // Charger ou créer le profil utilisateur
  const loadUserProfile = async (firebaseUser: User): Promise<UserProfile> => {
    try {
      const userDocRef = doc(db, 'users', firebaseUser.uid);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const data = userDoc.data();
        const profile: UserProfile = {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: firebaseUser.displayName,
          isAnonymous: firebaseUser.isAnonymous,
          subscription: {
            plan: data.subscription?.plan || 'free',
            status: data.subscription?.status || 'active',
            startDate: data.subscription?.startDate?.toDate() || new Date(),
            endDate: data.subscription?.endDate?.toDate(),
            trialEndDate: data.subscription?.trialEndDate?.toDate(),
            isTrialActive: data.subscription?.isTrialActive || false
          },
          createdAt: data.createdAt?.toDate() || new Date(),
          lastLoginAt: new Date(),
          preferences: data.preferences || {
            language: 'fr',
            theme: 'light',
            notifications: true
          }
        };

        // Vérifier et mettre à jour le statut de l'abonnement
        const updatedProfile = checkAndUpdateSubscriptionStatus(profile);

        // Mettre à jour la dernière connexion et le statut si nécessaire
        await updateDoc(userDocRef, {
          lastLoginAt: serverTimestamp(),
          ...(updatedProfile.subscription !== profile.subscription && {
            subscription: updatedProfile.subscription
          })
        });

        return updatedProfile;
      } else {
        // Créer un nouveau profil utilisateur
        const newProfile = createDefaultUserProfile(firebaseUser);
        await setDoc(userDocRef, {
          subscription: newProfile.subscription,
          createdAt: serverTimestamp(),
          lastLoginAt: serverTimestamp(),
          preferences: newProfile.preferences
        });
        return newProfile;
      }
    } catch (error) {
      console.error('Erreur lors du chargement du profil utilisateur:', error);
      // Retourner un profil par défaut en cas d'erreur
      return createDefaultUserProfile(firebaseUser);
    }
  };

  // Écouter les changements d'authentification
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);

      if (firebaseUser) {
        setUser(firebaseUser);
        const profile = await loadUserProfile(firebaseUser);
        setUserProfile(profile);
      } else {
        setUser(null);
        setUserProfile(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  // Fonctions d'authentification
  const signIn = async (email: string, password: string) => {
    try {
      await signInWithEmailAndPassword(auth, email, password);
    } catch (error: any) {
      console.error('Erreur de connexion:', error);
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  const signUp = async (email: string, password: string, displayName?: string) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      // Le profil sera créé automatiquement par l'écouteur onAuthStateChanged
    } catch (error: any) {
      console.error('Erreur d\'inscription:', error);
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  const signInWithGoogle = async () => {
    try {
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
    } catch (error: any) {
      console.error('Erreur de connexion Google:', error);
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  const signInAsGuest = async () => {
    try {
      await signInAnonymously(auth);
    } catch (error: any) {
      console.error('Erreur de connexion anonyme:', error);
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
    } catch (error: any) {
      console.error('Erreur de déconnexion:', error);
      throw error;
    }
  };

  const updateUserProfile = async (updates: Partial<UserProfile>) => {
    if (!user || !userProfile) return;

    try {
      const userDocRef = doc(db, 'users', user.uid);
      await updateDoc(userDocRef, updates);
      setUserProfile({ ...userProfile, ...updates });
    } catch (error) {
      console.error('Erreur de mise à jour du profil:', error);
      throw error;
    }
  };

  // Fonctions utilitaires
  const hasActivePremium = (): boolean => {
    if (!userProfile) return false;
    return userProfile.subscription.plan === 'premium' &&
           (userProfile.subscription.status === 'active' || userProfile.subscription.status === 'trial');
  };

  const getRemainingTrialDays = (): number => {
    if (!userProfile?.subscription.isTrialActive || !userProfile.subscription.trialEndDate) {
      return 0;
    }

    const now = new Date();
    const trialEnd = userProfile.subscription.trialEndDate;
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  };

  // Messages d'erreur traduits
  const getAuthErrorMessage = (errorCode: string): string => {
    switch (errorCode) {
      case 'auth/user-not-found':
      case 'auth/wrong-password':
      case 'auth/invalid-credential':
        return 'Email ou mot de passe incorrect';
      case 'auth/email-already-in-use':
        return 'Cette adresse email est déjà utilisée';
      case 'auth/weak-password':
        return 'Le mot de passe doit contenir au moins 6 caractères';
      case 'auth/invalid-email':
        return 'Adresse email invalide';
      case 'auth/too-many-requests':
        return 'Trop de tentatives. Veuillez réessayer plus tard';
      case 'auth/network-request-failed':
        return 'Erreur de connexion réseau';
      default:
        return 'Une erreur est survenue. Veuillez réessayer';
    }
  };

  const value: AuthContextType = {
    user,
    userProfile,
    loading,
    isAuthenticated: !!user,
    signIn,
    signUp,
    signInWithGoogle,
    signInAsGuest,
    signOut,
    updateUserProfile,
    hasActivePremium,
    getRemainingTrialDays
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};