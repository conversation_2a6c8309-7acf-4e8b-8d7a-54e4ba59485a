import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchSessions, syncSessionsWithFirestore } from '../store/slices/sessionsSlice';
import { fetchJournalEntries, syncJournalWithFirestore } from '../store/slices/journalSlice';
import { fetchAudioAssets } from '../store/slices/audioAssetsSlice';

/**
 * Hook personnalisé pour gérer la synchronisation automatique avec Firestore
 * Se déclenche quand l'utilisateur se connecte et synchronise périodiquement
 */
export const useFirestoreSync = () => {
  const { isAuthenticated, user } = useAuth();
  const dispatch = useAppDispatch();
  
  // Sélecteurs pour les changements en attente
  const sessionsPendingChanges = useAppSelector(state => state.sessions.pendingChanges);
  const journalPendingChanges = useAppSelector(state => state.journal.pendingChanges);
  const isOnline = useAppSelector(state => state.network.isOnline);

  // Synchronisation initiale quand l'utilisateur se connecte
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('User authenticated, starting initial sync...');
      
      // Charger les données depuis Firestore
      dispatch(fetchSessions() as any);
      dispatch(fetchJournalEntries() as any);
      dispatch(fetchAudioAssets() as any);
    }
  }, [isAuthenticated, user, dispatch]);

  // Synchronisation des changements en attente quand on revient en ligne
  useEffect(() => {
    if (isAuthenticated && isOnline) {
      // Synchroniser les sessions si il y a des changements en attente
      const hasSessionChanges = 
        sessionsPendingChanges.created.length > 0 ||
        sessionsPendingChanges.updated.length > 0 ||
        sessionsPendingChanges.deleted.length > 0;

      if (hasSessionChanges) {
        console.log('Syncing pending session changes...');
        dispatch(syncSessionsWithFirestore(sessionsPendingChanges) as any);
      }

      // Synchroniser le journal si il y a des changements en attente
      const hasJournalChanges = 
        journalPendingChanges.created.length > 0 ||
        journalPendingChanges.updated.length > 0 ||
        journalPendingChanges.deleted.length > 0;

      if (hasJournalChanges) {
        console.log('Syncing pending journal changes...');
        dispatch(syncJournalWithFirestore(journalPendingChanges) as any);
      }
    }
  }, [isAuthenticated, isOnline, sessionsPendingChanges, journalPendingChanges, dispatch]);

  // Synchronisation périodique (toutes les 5 minutes)
  useEffect(() => {
    if (!isAuthenticated || !isOnline) return;

    const syncInterval = setInterval(() => {
      console.log('Performing periodic sync...');
      
      // Re-fetch data to get latest updates
      dispatch(fetchSessions() as any);
      dispatch(fetchJournalEntries() as any);
      
      // Sync any pending changes
      const hasSessionChanges = 
        sessionsPendingChanges.created.length > 0 ||
        sessionsPendingChanges.updated.length > 0 ||
        sessionsPendingChanges.deleted.length > 0;

      if (hasSessionChanges) {
        dispatch(syncSessionsWithFirestore(sessionsPendingChanges) as any);
      }

      const hasJournalChanges = 
        journalPendingChanges.created.length > 0 ||
        journalPendingChanges.updated.length > 0 ||
        journalPendingChanges.deleted.length > 0;

      if (hasJournalChanges) {
        dispatch(syncJournalWithFirestore(journalPendingChanges) as any);
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(syncInterval);
  }, [isAuthenticated, isOnline, sessionsPendingChanges, journalPendingChanges, dispatch]);

  return {
    isAuthenticated,
    isOnline,
    hasPendingChanges: 
      sessionsPendingChanges.created.length > 0 ||
      sessionsPendingChanges.updated.length > 0 ||
      sessionsPendingChanges.deleted.length > 0 ||
      journalPendingChanges.created.length > 0 ||
      journalPendingChanges.updated.length > 0 ||
      journalPendingChanges.deleted.length > 0
  };
};
