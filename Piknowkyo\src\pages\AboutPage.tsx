// src/pages/AboutPage.tsx

import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import {
  FiHeart, FiBookOpen, FiBarChart2, FiUsers, FiSettings, FiSmile, FiStar, FiGlobe, FiBell
} from 'react-icons/fi';

import heroBgLight from '../assets/images/hero-background_light.webp';
import heroBgDark from '../assets/images/hero-background_dark.webp';

// Importez votre hook useTheme personnalisé si vous l'avez créé dans ThemeProvider.tsx
// import { useTheme } from '../ThemeProvider'; // Ajustez le chemin si nécessaire

const PageContainer = styled.div`
  padding: 2rem 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

// CORRECTION: Interface pour les props de HeroSection
interface HeroSectionProps {
  $bgimage?: string; // Utiliser la prop transitoire $bgimage
}

// CORRECTION: Utilisation de HeroSectionProps et de $bgimage
const HeroSection = styled.section<HeroSectionProps>`
  text-align: center;
  margin-bottom: 3rem;
  padding: 4rem 1rem;

  background-image: ${({ $bgimage, theme }: { $bgimage?: string; theme: DefaultTheme }) =>
    $bgimage
      ? `linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url(${$bgimage})`
      : `linear-gradient(135deg, ${theme.gradientStart || theme.primary}, ${theme.gradientEnd || theme.accent})`
  };

  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;

  color: ${({ theme }) => theme.textLight || '#fff'};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow || '0 4px 12px rgba(0,0,0,0.1)'};
  position: relative;

  h1, p {
    position: relative;
    z-index: 2; // S'assurer que le texte est au-dessus de l'overlay/image
  }

  h1 {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }
  p {
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto 1.5rem auto;
    line-height: 1.7;
    text-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }
`;

const Section = styled.section`
  margin-bottom: 3.5rem;

  h2 {
    font-size: 2rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background-color: ${({ theme }) => theme.accent};
      border-radius: 2px;
    }
  }
`;

const PhilosophyList = styled.ul`
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 800px;
  margin: 0 auto 1.5rem auto;

  li {
    background: ${({ theme }) => theme.surfaceAlt};
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: ${({ theme }) => theme.cardShadow};
    p {
      margin-top: 0.5rem;
      color: ${({ theme }) => theme.textSecondary};
      line-height: 1.6;
      &:last-child {
          margin-bottom: 0;
      }
    }
    strong {
      color: ${({ theme }) => theme.primary};
      font-size: 1.2rem;
      display: block;
      margin-bottom: 0.25rem;
    }
  }
`;

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  max-width: 700px;
  margin: 1.5rem auto;

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  li {
    display: flex;
    align-items: flex-start;
    background: ${({ theme }) => theme.surfaceAlt};
    padding: 1rem 1.5rem;
    border-radius: 10px;
    font-size: 0.95rem;
    color: ${({ theme }) => theme.textSecondary};

    svg {
      font-size: 1.5rem;
      color: ${({ theme }) => theme.accent};
      margin-right: 1rem;
      flex-shrink: 0;
      margin-top: 0.1em;
    }
  }
`;

const AboutPage: React.FC = () => {
  const { t } = useTranslation();
  // Obtenir l'objet thème actuel de styled-components
  const currentTheme = useContext(ThemeContext) as DefaultTheme;

  // Déterminer si le mode sombre est actif EN SE BASANT SUR LA PROPRIÉTÉ 'name' DU THÈME
  // Assurez-vous que vos objets lightTheme et darkTheme ont une propriété 'name: "light"' ou 'name: "dark"'
  const isDarkMode = currentTheme.name === 'dark';
  const heroImageUrl = isDarkMode ? heroBgDark : heroBgLight;

  return (
    <PageContainer>
      {/* CORRECTION: Utilisation de la prop transitoire $bgimage */}
      <HeroSection $bgimage={heroImageUrl}>
        <h1>{t('about.title')}</h1>
        <p>
          {t('about.description')}
        </p>
      </HeroSection>

      <Section>
        <h2>{t('about.philosophy.title')}</h2>
        <PhilosophyList>
          <li>
            <strong>{t('about.philosophy.pi.title')}</strong>
            <p>{t('about.philosophy.pi.description')}</p>
          </li>
          <li>
            <strong>{t('about.philosophy.know.title')}</strong>
            <p>{t('about.philosophy.know.description')}</p>
          </li>
          <li>
            <strong>{t('about.philosophy.kyo.title')}</strong>
            <p>{t('about.philosophy.kyo.description')}</p>
          </li>
        </PhilosophyList>
        <p style={{ textAlign: 'center', marginTop: '2rem', fontStyle: 'italic', color: currentTheme.textSecondary }}>
          {t('about.philosophy.conclusion')} <a href="https://piknowkyo.com" target="_blank" rel="noopener noreferrer" style={{color: currentTheme.primary, fontWeight: 'bold'}}>piknowkyo.com</a>.
        </p>
      </Section>

      <Section>
        <h2>{t('about.tools.title')}</h2>
        <p style={{textAlign: 'center', marginBottom: '1.5rem', maxWidth: '700px', margin: '0 auto 1.5rem auto'}}>
            {t('about.tools.description')}
        </p>
        <FeatureList>
            <li><FiSmile /> {t('about.tools.hypnosis')}</li>
            <li><FiHeart /> {t('about.tools.meditation')}</li>
            <li><FiStar /> {t('about.tools.affirmations')}</li>
            <li><FiUsers /> {t('about.tools.nlp')}</li>
            <li><FiBookOpen /> {t('about.tools.stories')}</li>
            <li><FiSettings /> {t('about.features.customizable')}</li>
        </FeatureList>
      </Section>


      <Section>
        <h2>{t('about.experience.title')}</h2>
        <FeatureList>
          <li><FiSettings /> {t('about.experience.audio')}</li>
          <li><FiBookOpen /> {t('about.features.journal')}</li>
          <li><FiBarChart2 /> {t('about.features.stats')}</li>
          <li><FiStar /> {t('about.experience.guidedPaths')}</li>
          <li><FiUsers /> {t('about.experience.community')}</li>
          <li><FiHeart /> {t('about.experience.blog')}</li>
          <li><FiGlobe /> {t('about.experience.multilang')}</li>
          <li><FiBell /> {t('about.experience.notifications')}</li>
        </FeatureList>
         <p style={{ textAlign: 'center', marginTop: '2rem', fontSize: '0.9em', color: currentTheme.textSecondary }}>
          <strong>{t('about.monetization.title')}</strong> {t('about.monetization.description')}
        </p>
      </Section>

      <Section>
        <h2>{t('about.community.title')}</h2>
        <p style={{ textAlign: 'center', maxWidth: '700px', margin: '0 auto'}}>
          {t('about.community.description')}
        </p>
        <p style={{
            textAlign: 'center',
            marginTop: '2rem',
            fontSize: '0.95em',
            color: currentTheme.textMuted || '#555'
          }}>
            {t('about.community.contact')}
            <br />
            <a
              href="mailto:<EMAIL>"
              style={{
                color: currentTheme.primary,
                fontWeight: 'bold',
                textDecoration: 'underline',
                display: 'inline-block',
                marginTop: '0.5rem'
              }}
            >
              <EMAIL>
            </a>
        </p>
        <p style={{ textAlign: 'center', marginTop: '1rem', fontSize: '0.9em', color: currentTheme.textMuted || '#666' }}>
            {t('about.community.website')} <a href="https://piknowkyo.com" target="_blank" rel="noopener noreferrer" style={{color: currentTheme.primary, fontWeight: 'bold'}}>piknowkyo.com</a> {t('about.community.moreInfo')}.
        </p>
      </Section>
    </PageContainer>
  );
};

export default AboutPage;