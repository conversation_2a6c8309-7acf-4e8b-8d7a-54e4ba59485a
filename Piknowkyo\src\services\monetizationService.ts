import { SessionRestrictions, GameRestrictions, SubscriptionPlan, TemporaryUnlock } from '../models';

// Configuration des restrictions par plan
export const PLAN_RESTRICTIONS = {
  free: {
    sessions: {
      allowedTypes: ['meditation', 'story'],
      allowedAudioFeatures: {
        music: true,
        ambient: false,
        binaural: false,
        voice: true, // voix de base seulement
      },
    } as SessionRestrictions,
    games: {
      allowedGames: [],
      maxPlayTimeMinutes: 0,
    } as GameRestrictions,
  },
  premium: {
    sessions: {
      allowedTypes: ['meditation', 'story', 'hypnosis', 'pnl', 'relaxation', 'breathing', 'custom'],
      allowedAudioFeatures: {
        music: true,
        ambient: true,
        binaural: true,
        voice: true,
      },
    } as SessionRestrictions,
    games: {
      allowedGames: ['zen-tetris', 'memory-challenge', 'breathing-game'], // tous les jeux
      maxPlayTimeMinutes: Infinity,
    } as GameRestrictions,
  },
};

// Durées des unlock temporaires
export const UNLOCK_DURATIONS = {
  session: 4 * 60 * 60 * 1000, // 4 heures en millisecondes
  game: 1 * 60 * 60 * 1000,    // 1 heure en millisecondes
};

export class MonetizationService {
  /**
   * Vérifie si un utilisateur peut accéder à une session
   */
  static canAccessSession(
    sessionType: string,
    sessionId: string,
    currentPlan: SubscriptionPlan,
    temporaryUnlocks: TemporaryUnlock[]
  ): boolean {
    // Si premium, accès total
    if (currentPlan === 'premium') {
      return true;
    }

    // Vérifier les restrictions du plan gratuit
    const restrictions = PLAN_RESTRICTIONS.free.sessions;
    if (restrictions.allowedTypes.includes(sessionType)) {
      return true;
    }

    // Vérifier les unlock temporaires
    const now = Date.now();
    const hasValidUnlock = temporaryUnlocks.some(unlock =>
      unlock.type === 'session' &&
      unlock.expiresAt > now &&
      (unlock.sessionId === sessionId || !unlock.sessionId) // unlock global ou spécifique
    );

    return hasValidUnlock;
  }

  /**
   * Vérifie si un utilisateur peut accéder aux fonctionnalités audio avancées
   * IMPORTANT: Les sons binauraux et ambiants sont EXCLUSIVEMENT premium
   * et ne peuvent PAS être débloqués temporairement
   */
  static canAccessAudioFeature(
    feature: keyof SessionRestrictions['allowedAudioFeatures'],
    currentPlan: SubscriptionPlan
  ): boolean {
    // Les fonctionnalités audio premium sont EXCLUSIVEMENT pour les abonnements premium
    // Pas d'unlock temporaire possible pour ambient et binaural
    if (currentPlan === 'premium') {
      return true;
    }

    return PLAN_RESTRICTIONS.free.sessions.allowedAudioFeatures[feature];
  }

  /**
   * Vérifie si une fonctionnalité audio est exclusivement premium
   * (ne peut pas être débloquée temporairement)
   */
  static isAudioFeatureExclusivelyPremium(
    feature: keyof SessionRestrictions['allowedAudioFeatures']
  ): boolean {
    return feature === 'ambient' || feature === 'binaural';
  }

  /**
   * Vérifie si un utilisateur peut accéder à un jeu
   */
  static canAccessGame(
    gameId: string,
    currentPlan: SubscriptionPlan,
    temporaryUnlocks: TemporaryUnlock[]
  ): boolean {
    // Si premium, accès total
    if (currentPlan === 'premium') {
      return true;
    }

    // Vérifier les unlock temporaires
    const now = Date.now();
    const hasValidUnlock = temporaryUnlocks.some(unlock =>
      unlock.type === 'game' &&
      unlock.expiresAt > now
    );

    return hasValidUnlock;
  }

  /**
   * Crée un unlock temporaire après avoir regardé une vidéo
   */
  static createTemporaryUnlock(type: 'session' | 'game', sessionId?: string): TemporaryUnlock {
    const duration = UNLOCK_DURATIONS[type];
    return {
      type,
      expiresAt: Date.now() + duration,
      sessionId,
    };
  }

  /**
   * Vérifie si un unlock temporaire est encore valide
   */
  static isUnlockValid(unlock: TemporaryUnlock): boolean {
    return unlock.expiresAt > Date.now();
  }

  /**
   * Calcule le temps restant pour un unlock en minutes
   */
  static getUnlockTimeRemaining(unlock: TemporaryUnlock): number {
    const remaining = unlock.expiresAt - Date.now();
    return Math.max(0, Math.floor(remaining / (60 * 1000))); // en minutes
  }

  /**
   * Simule le visionnage d'une vidéo publicitaire
   */
  static async watchAdvertisement(): Promise<boolean> {
    // Simulation - dans une vraie app, ceci intégrerait avec un SDK publicitaire
    return new Promise((resolve) => {
      // Simuler un délai de chargement
      setTimeout(() => {
        // Simuler un succès (90% de chance)
        const success = Math.random() > 0.1;
        resolve(success);
      }, 2000);
    });
  }

  /**
   * Filtre les sessions selon les restrictions du plan
   */
  static filterSessionsByPlan(
    sessions: any[],
    currentPlan: SubscriptionPlan,
    temporaryUnlocks: TemporaryUnlock[]
  ): any[] {
    if (currentPlan === 'premium') {
      return sessions;
    }

    return sessions.filter(session =>
      this.canAccessSession(session.type, session.id, currentPlan, temporaryUnlocks)
    );
  }

  /**
   * Détermine si une session nécessite un upgrade ou un unlock temporaire
   */
  static getSessionAccessStatus(
    sessionType: string,
    sessionId: string,
    currentPlan: SubscriptionPlan,
    temporaryUnlocks: TemporaryUnlock[]
  ): 'allowed' | 'needs_unlock' | 'needs_premium' {
    if (this.canAccessSession(sessionType, sessionId, currentPlan, temporaryUnlocks)) {
      return 'allowed';
    }

    if (currentPlan === 'free') {
      return 'needs_unlock';
    }

    return 'needs_premium';
  }
}
