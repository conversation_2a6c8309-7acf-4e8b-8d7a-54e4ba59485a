import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

const Card = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  margin: 1rem 0;
  padding: 1.2rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 80px;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  padding: 0.7rem;
  font-size: 1rem;
  margin-bottom: 0.5rem;
`;

const Button = styled.button`
  background: ${({ theme }) => theme.primary};
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.7rem 1.5rem;
  font-size: 1rem;
  margin-top: 0.5rem;
  cursor: pointer;
`;

const JournalEntryForm: React.FC<{ sessionId: string; onSave: (note: string) => void }> = ({ sessionId, onSave }) => {
  const { t } = useTranslation();
  const [note, setNote] = useState('');
  return (
    <Card>
      <h3>{t('journal.trackingJournal')}</h3>
      <TextArea
        placeholder={t('journal.addEntry')}
        value={note}
        onChange={e => setNote(e.target.value)}
      />
      <Button onClick={() => { if (note.trim()) { onSave(note); setNote(''); } }}>{t('common.save')}</Button>
    </Card>
  );
};

export default JournalEntryForm;
