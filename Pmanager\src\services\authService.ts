import {
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User,
  GoogleAuthProvider,
  signInWithPopup
} from 'firebase/auth';
import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '@/config/firebase';
import { AdminUser, AdminRole, Permission } from '@/types';

class AuthService {
  private static instance: AuthService;

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // Connexion avec email/mot de passe
  async signInWithEmail(email: string, password: string): Promise<AdminUser | null> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const adminUser = await this.getAdminUser(userCredential.user.uid);

      if (!adminUser) {
        // Déconnecter l'utilisateur s'il n'est pas admin
        await signOut(auth);
        throw new Error('Utilisateur non autorisé pour l\'administration');
      }

      if (!adminUser.isActive) {
        // Déconnecter l'utilisateur si le compte est désactivé
        await signOut(auth);
        throw new Error('Compte administrateur désactivé');
      }

      // Mettre à jour la dernière connexion
      await this.updateLastLogin(adminUser.uid);

      return adminUser;
    } catch (error: any) {
      console.error('Erreur de connexion:', error);

      // Traduire les erreurs Firebase en français
      if (error.code) {
        switch (error.code) {
          case 'auth/invalid-credential':
          case 'auth/wrong-password':
          case 'auth/user-not-found':
            throw new Error('Email ou mot de passe incorrect');
          case 'auth/invalid-email':
            throw new Error('Adresse email invalide');
          case 'auth/user-disabled':
            throw new Error('Ce compte a été désactivé');
          case 'auth/too-many-requests':
            throw new Error('Trop de tentatives de connexion. Veuillez réessayer plus tard');
          case 'auth/network-request-failed':
            throw new Error('Erreur de connexion réseau');
          default:
            throw new Error(error.message || 'Erreur de connexion inconnue');
        }
      }

      throw error;
    }
  }

  // Connexion avec Google
  async signInWithGoogle(): Promise<AdminUser | null> {
    try {
      const provider = new GoogleAuthProvider();
      const userCredential = await signInWithPopup(auth, provider);
      const adminUser = await this.getAdminUser(userCredential.user.uid);

      if (!adminUser) {
        throw new Error('Utilisateur non autorisé pour l\'administration');
      }

      if (!adminUser.isActive) {
        throw new Error('Compte administrateur désactivé');
      }

      await this.updateLastLogin(adminUser.uid);

      return adminUser;
    } catch (error) {
      console.error('Erreur de connexion Google:', error);
      throw error;
    }
  }

  // Déconnexion
  async signOut(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Erreur de déconnexion:', error);
      throw error;
    }
  }

  // Récupérer les données d'un utilisateur admin
  async getAdminUser(uid: string): Promise<AdminUser | null> {
    try {
      const adminDoc = await getDoc(doc(db, 'admin_users', uid));

      if (!adminDoc.exists()) {
        return null;
      }

      const data = adminDoc.data();
      return {
        uid,
        email: data.email,
        displayName: data.displayName,
        role: data.role,
        permissions: data.permissions || [],
        createdAt: data.createdAt?.toDate() || new Date(),
        lastLogin: data.lastLogin?.toDate(),
        isActive: data.isActive ?? true,
      };
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'utilisateur admin:', error);
      return null;
    }
  }

  // Créer un nouvel utilisateur admin
  async createAdminUser(
    uid: string,
    email: string,
    displayName: string,
    role: AdminRole,
    permissions: Permission[]
  ): Promise<void> {
    try {
      const adminUser: Omit<AdminUser, 'uid'> = {
        email,
        displayName,
        role,
        permissions,
        createdAt: new Date(),
        isActive: true,
      };

      await setDoc(doc(db, 'admin_users', uid), adminUser);
    } catch (error) {
      console.error('Erreur lors de la création de l\'utilisateur admin:', error);
      throw error;
    }
  }

  // Mettre à jour la dernière connexion
  private async updateLastLogin(uid: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'admin_users', uid), {
        lastLogin: new Date(),
      });
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la dernière connexion:', error);
    }
  }

  // Vérifier les permissions
  hasPermission(user: AdminUser, permission: Permission): boolean {
    if (user.role === 'super_admin') {
      return true; // Super admin a toutes les permissions
    }

    return user.permissions.includes(permission);
  }

  // Écouter les changements d'authentification
  onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  // Obtenir l'utilisateur actuel
  getCurrentUser(): User | null {
    return auth.currentUser;
  }
}

export default AuthService.getInstance();
