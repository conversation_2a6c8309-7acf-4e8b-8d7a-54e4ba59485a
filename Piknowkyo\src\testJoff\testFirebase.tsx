import { useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';
import { addScript, getScripts } from "../services/scriptsService";
import type { Script } from "../models/script.model";

const TestFirebase: React.FC = () => {
    const { t } = useTranslation();
    const [newScriptName, setNewScriptName] = useState('');
    const [scripts, setScripts] = useState<Script[]>([]);

  useEffect(() => {
    let unsubscribe: () => void;
    unsubscribe = getScripts(setScripts);
    return () => {
      if (unsubscribe) unsubscribe();
    }}, []);

const handleAddScript = async (e: React.FormEvent) => {
    e.preventDefault();
    const newScript: Omit<Script, 'id'> = {
        name: newScriptName
      };
    await addScript(newScript);
};

    return (
        <div>
           <div className="card">
            {scripts.map((script) => (
      <p key={script.id}>
    {script.name}
  </p>
))}

      </div>
    <form onSubmit={handleAddScript}>
    <input
      type="text"
      value={newScriptName}
      onChange={(e) => setNewScriptName(e.target.value)}
      placeholder={t('test.newSongTitle')}
    />
    <button type="submit">{t('test.addNewSong')}</button>
  </form>

        </div>
    );


}

export default TestFirebase