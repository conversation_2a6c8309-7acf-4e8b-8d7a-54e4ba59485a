// src/pages/SessionDetailPage.tsx

import React, { useEffect, useState, useContext, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { fetchSessionWithScript } from '../data/sessions';
import { Session, SessionAudioConfig } from '../models';
import JournalEntryForm from '../components/JournalEntryForm';
import {
  FiClock, FiHeart, FiTag, FiMessageSquare, FiPlayCircle, FiChevronLeft, FiStar, FiLoader, FiInfo, FiSettings, FiEdit3,
  FiBellOff, FiBell // Icônes pour NPD
} from 'react-icons/fi';
import { useLang } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import AudioConfigPanel from '../components/AudioConfigPanel'; // <--- IMPORT DU PANNEAU DE CONFIGURATION AUDIO

// --- Helper pour les permissions de notification (Web) ---
const checkNotificationPermission = (): NotificationPermission => {
  if (!("Notification" in window)) {
    return "denied";
  }
  return Notification.permission;
};

const requestNotificationPermission = async (t: any): Promise<NotificationPermission> => {
  if (!("Notification" in window)) {
    alert(t('notifications.notSupported', 'Ce navigateur ne supporte pas les notifications.'));
    return "denied";
  }
  if (Notification.permission === "granted") {
    return "granted";
  }
  if (Notification.permission !== "denied") {
    const permission = await Notification.requestPermission();
    return permission;
  }
  return "denied";
};


// --- Styled Components (les vôtres, inchangés) ---
const PageContainer = styled.div`
  padding-bottom: calc(4rem + 60px + 1.5rem);
`;

const HeaderImage = styled.div<{ imageUrl?: string }>`
  height: 35vh;
  min-height: 280px;
  max-height: 450px;
  background-image: ${({ imageUrl, theme }) =>
    imageUrl
      ? `url(${imageUrl})`
      : `linear-gradient(135deg, ${theme.gradientStart || theme.primary}, ${theme.gradientEnd || theme.accent})`
  };
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-end;
  padding: 1.5rem;
  position: relative;
  color: ${({ theme }) => theme.textLight || '#fff'};

  &::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.75) 0%, rgba(0,0,0,0.15) 60%, rgba(0,0,0,0) 100%);
    z-index: 1;
  }
`;

const HeaderContent = styled.div`
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;

  h1 {
    font-size: 2.4rem;
    margin: 0 0 0.5rem 0;
    font-weight: 700;
    text-shadow: 0 2px 5px rgba(0,0,0,0.6);
  }
`;

const BackButton = styled.button`
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background: rgba(0,0,0,0.4);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 30;
  transition: background-color 0.2s;
  font-size: 1.3rem;

  &:hover {
    background: rgba(0,0,0,0.6);
  }
`;

const SessionInfoBar = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem 1.2rem;
  align-items: center;
  font-size: 0.95rem;
  opacity: 0.95;

  svg {
    margin-right: 0.4rem;
    vertical-align: middle;
  }

  span {
    display: flex;
    align-items: center;
  }
`;

const ContentWrapper = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: -4rem auto 0 auto;
  background-color: ${({ theme }) => theme.surface};
  border-radius: 20px;
  box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
  position: relative;
  z-index: 10;
`;

const Section = styled.section`
  margin-bottom: 2.5rem;

  &:last-child {
    margin-bottom: 0;
  }

  h2 {
    font-size: 1.6rem;
    color: ${({ theme }) => theme.primary};
    margin-top: 0;
    margin-bottom: 1.2rem;
    padding-bottom: 0.6rem;
    border-bottom: 1px solid ${({ theme }) => theme.border};
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  p, li {
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.75;
    font-size: 1rem;
  }

  ul {
    list-style: disc;
    padding-left: 1.8rem;
  }
`;

const TagsContainer = styled.div`
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  span {
    background-color: ${({ theme }) => `${theme.accent}2A`};
    color: ${({ theme }) => theme.accent};
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
  }
`;

const CommentList = styled.ul`
  list-style: none;
  padding: 0;
  li {
    background-color: ${({ theme }) => theme.surfaceAlt};
    padding: 1rem 1.2rem;
    border-radius: 10px;
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
    border-left: 3px solid ${({ theme }) => theme.primary};
  }
`;

const PlayButtonFloating = styled.button`
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom, 0px) + 60px + 1.5rem);
  right: 1.5rem;
  background-color: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight || '#fff'};
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0,0,0,0.25);
  cursor: pointer;
  z-index: 100;
  transition: transform 0.2s, background-color 0.2s;

  &:hover {
    transform: scale(1.08);
    background-color: ${({ theme }) => theme.accent || theme.primary};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.primary};
  min-height: 50vh;

  svg {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.p`
  color: red;
  text-align: center;
  padding: 2rem;
`;

// Styles pour la section DND (gardés si besoin spécifique, sinon AudioToggleGroup est générique)
const AudioToggleGroup = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.surfaceAlt};
  padding: 0.8rem 1.2rem;
  border-radius: 10px;
  border: 1px solid ${({ theme }) => theme.border};
  margin-bottom: 1rem; // Ajout d'un margin pour séparer du panneau suivant

  label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    color: ${({ theme }) => theme.text};
    cursor: pointer;
    flex-grow: 1;
  }

  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    margin-left: 10px;
  }

  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: ${({ theme }) => theme.textMuted || '#ccc'};
    transition: .4s;
    border-radius: 24px;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + .slider {
    background-color: ${({ theme }) => theme.primary};
  }

  input:focus + .slider {
    box-shadow: 0 0 1px ${({ theme }) => theme.primary};
  }

  input:checked + .slider:before {
    transform: translateX(20px);
  }
`;

const InfoTextSmall = styled.p`
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  margin-top: -0.5rem; // Ajustement pour être sous le toggle DND
  margin-bottom: 1rem; // Espace avant AudioConfigPanel
`;


const SessionDetailPage: React.FC = () => {
  const { t } = useTranslation();
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { lang: appLang } = useLang();

  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [journalNotes, setJournalNotes] = useState<string[]>([]);

  // States pour les overrides audio, maintenant contrôlés en partie par AudioConfigPanel
  const [enableMusic, setEnableMusic] = useState(false);
  const [musicVolume, setMusicVolume] = useState(0.5);
  const [musicFileUrl, setMusicFileUrl] = useState<string | undefined>(undefined);

  const [enableAmbient, setEnableAmbient] = useState(false);
  const [ambientVolume, setAmbientVolume] = useState(0.3);
  const [ambientFileUrl, setAmbientFileUrl] = useState<string | undefined>(undefined);

  const [enableBinaural, setEnableBinaural] = useState(false);
  const [binauralVolume, setBinauralVolume] = useState(0.2);
  const [baseFreq, setBaseFreq] = useState(100);
  const [beatFreq, setBeatFreq] = useState(10);

  const [voiceVolume, setVoiceVolume] = useState(1);
  // Si AudioConfigPanel gère le genre de la voix, ajouter un état ici aussi
  // const [voiceGender, setVoiceGender] = useState<'masculine' | 'feminine' | 'auto'>('auto');

  // State pour "Ne Pas Déranger" (reste géré ici)
  const [enableDND, setEnableDND] = useState(false);
  const [notificationPermissionStatus, setNotificationPermissionStatus] = useState<NotificationPermission>("default");

  useEffect(() => {
    setNotificationPermissionStatus(checkNotificationPermission());
  }, []);

  // Effet pour charger la session et initialiser les états audio
  useEffect(() => {
    if (!sessionId) {
      setError(t('errors.missingSessionId', "ID de session manquant."));
      setIsLoading(false);
      return;
    }
    const loadSession = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const sessionData = await fetchSessionWithScript(sessionId, appLang);
        if (sessionData) {
          setSession(sessionData);

          // Initialisation des valeurs audio par défaut à partir de la session
          let initialEnableMusic = sessionData.audio?.enableMusic ?? false;
          let initialMusicVolume = sessionData.audio?.music?.volume ?? 0.5;
          let initialMusicFileUrl = sessionData.audio?.music?.url;

          let initialEnableAmbient = sessionData.audio?.enableAmbient ?? false;
          let initialAmbientVolume = sessionData.audio?.ambient?.volume ?? 0.3;
          let initialAmbientFileUrl = sessionData.audio?.ambient?.url;

          let initialEnableBinaural = sessionData.audio?.enableBinaural ?? false;
          let initialBinauralVolume = sessionData.audio?.binaural?.volume ?? 0.2;
          let initialBaseFreq = sessionData.audio?.binaural?.baseFreq ?? 100;
          let initialBeatFreq = sessionData.audio?.binaural?.beatFreq ?? 10;

          let initialVoiceVolume = sessionData.audio?.voice?.volume ?? 1;
          // let initialVoiceGender = sessionData.audio?.voice?.gender ?? 'auto';
          let initialEnableDND = false;

          const savedOverridesRaw = localStorage.getItem(`session_audio_overrides_${sessionId}`);
          if (savedOverridesRaw) {
            try {
              const parsedOverrides = JSON.parse(savedOverridesRaw);
              initialEnableMusic = parsedOverrides.enableMusic ?? initialEnableMusic;
              initialMusicVolume = parsedOverrides.musicVolume ?? initialMusicVolume;
              initialMusicFileUrl = parsedOverrides.musicFileUrl ?? initialMusicFileUrl;

              initialEnableAmbient = parsedOverrides.enableAmbient ?? initialEnableAmbient;
              initialAmbientVolume = parsedOverrides.ambientVolume ?? initialAmbientVolume;
              initialAmbientFileUrl = parsedOverrides.ambientFileUrl ?? initialAmbientFileUrl;

              initialEnableBinaural = parsedOverrides.enableBinaural ?? initialEnableBinaural;
              initialBinauralVolume = parsedOverrides.binauralVolume ?? initialBinauralVolume;
              initialBaseFreq = parsedOverrides.baseFreq ?? initialBaseFreq;
              initialBeatFreq = parsedOverrides.beatFreq ?? initialBeatFreq;

              initialVoiceVolume = parsedOverrides.voiceVolume ?? initialVoiceVolume;
              // initialVoiceGender = parsedOverrides.voiceGender ?? initialVoiceGender;
              initialEnableDND = parsedOverrides.enableDND ?? initialEnableDND;

            } catch (e) {
              console.error("Erreur de parsing des overrides audio sauvegardés:", e);
              // Les valeurs par défaut de la session seront utilisées
            }
          }

          setEnableMusic(initialEnableMusic);
          setMusicVolume(initialMusicVolume);
          setMusicFileUrl(initialMusicFileUrl);

          setEnableAmbient(initialEnableAmbient);
          setAmbientVolume(initialAmbientVolume);
          setAmbientFileUrl(initialAmbientFileUrl);

          setEnableBinaural(initialEnableBinaural);
          setBinauralVolume(initialBinauralVolume);
          setBaseFreq(initialBaseFreq);
          setBeatFreq(initialBeatFreq);

          setVoiceVolume(initialVoiceVolume);
          // setVoiceGender(initialVoiceGender);
          setEnableDND(initialEnableDND);

          const notesRaw = localStorage.getItem('piknowkyo_journal');
          const allJournalEntries: Record<string, string[]> = notesRaw ? JSON.parse(notesRaw) : {};
          setJournalNotes(allJournalEntries[sessionId] || []);
        } else {
          setError(t('errors.sessionNotFoundWithId', `Session avec l'ID "{{id}}" non trouvée pour la langue {{lang}}.`, {id: sessionId, lang: appLang}));
        }
      } catch (err) {
        console.error("Error loading session details:", err);
        setError(err instanceof Error ? err.message : t('errors.cantLoadSessionDetails', "Impossible de charger les détails de la session."));
      } finally {
        setIsLoading(false);
      }
    };
    loadSession();
  }, [sessionId, appLang, t]);

  // Sauvegarder les overrides (y compris DND et les nouvelles propriétés de AudioConfigPanel)
  useEffect(() => {
    if (!sessionId || isLoading) return; // Ne pas sauvegarder pendant le chargement initial pour éviter de surcharger avec des valeurs potentiellement incomplètes
    const currentAudioOverrides = {
      enableMusic, musicVolume, musicFileUrl,
      enableAmbient, ambientVolume, ambientFileUrl,
      enableBinaural, binauralVolume, baseFreq, beatFreq,
      voiceVolume, /* voiceGender, */
      enableDND
    };
    localStorage.setItem(`session_audio_overrides_${sessionId}`, JSON.stringify(currentAudioOverrides));
  }, [
    sessionId, isLoading, // Ajout de isLoading
    enableMusic, musicVolume, musicFileUrl,
    enableAmbient, ambientVolume, ambientFileUrl,
    enableBinaural, binauralVolume, baseFreq, beatFreq,
    voiceVolume, /* voiceGender, */
    enableDND
  ]);

  // Handler pour les changements provenant de AudioConfigPanel
  const handleAudioConfigChange = (newConfig: SessionAudioConfig) => {
    setEnableMusic(newConfig.enableMusic ?? false);
    if (newConfig.music) {
      setMusicVolume(newConfig.music.volume ?? 0.5);
      setMusicFileUrl(newConfig.music.url); // AudioConfigPanel peut définir/modifier l'URL
    } else {
      setMusicFileUrl(undefined); // Si la musique est désactivée ou pas d'URL
    }

    setEnableAmbient(newConfig.enableAmbient ?? false);
    if (newConfig.ambient) {
      setAmbientVolume(newConfig.ambient.volume ?? 0.3);
      setAmbientFileUrl(newConfig.ambient.url);
    } else {
      setAmbientFileUrl(undefined);
    }

    setEnableBinaural(newConfig.enableBinaural ?? false);
    if (newConfig.binaural) {
      setBinauralVolume(newConfig.binaural.volume ?? 0.2);
      setBaseFreq(newConfig.binaural.baseFreq ?? 100);
      setBeatFreq(newConfig.binaural.beatFreq ?? 10);
    }

    if (newConfig.voice) {
      setVoiceVolume(newConfig.voice.volume ?? 1);
      // if (newConfig.voice.gender) setVoiceGender(newConfig.voice.gender);
    }
  };

  const handlePlaySession = () => {
    if (session) {
      navigate(`/player/${session.id}`);
    }
  };

  const handleSaveJournalNote = (note: string) => {
    if (!session || !sessionId) return;
    const newNotes = [note, ...journalNotes];
    setJournalNotes(newNotes);
    const notesRaw = localStorage.getItem('piknowkyo_journal');
    const allJournalEntries: Record<string, string[]> = notesRaw ? JSON.parse(notesRaw) : {};
    allJournalEntries[sessionId] = newNotes;
    localStorage.setItem('piknowkyo_journal', JSON.stringify(allJournalEntries));
  };

  const benefitsArray: string[] = useMemo(() => {
    return session?.benefits || [];
  }, [session]);

  const handleToggleDND = async () => {
    const newState = !enableDND;
    setEnableDND(newState);

    if (newState) {
      if (notificationPermissionStatus === 'default') {
        const permission = await requestNotificationPermission(t);
        setNotificationPermissionStatus(permission);
        if (permission === 'denied') {
          alert(t('sessionDetails.dnd.permissionDeniedAlert', "La permission pour les notifications a été refusée. Le mode 'Ne Pas Déranger' de l'application sera actif, mais les notifications du système pourraient toujours apparaître."));
        } else if (permission === 'granted') {
          console.log("Mode NPD (simulation Web) activé. Les notifications de l'application seront bloquées.");
        }
      } else if (notificationPermissionStatus === 'granted') {
        console.log("Mode NPD (simulation Web) activé. Les notifications de l'application seront bloquées.");
      } else if (notificationPermissionStatus === 'denied') {
         alert(t('sessionDetails.dnd.permissionDeniedInfo', "La permission pour les notifications est refusée. Le mode NPD de l'application sera actif."));
      }
    } else {
      console.log("Mode NPD (simulation Web) désactivé.");
    }
  };


  if (isLoading) {
    return <LoadingContainer><FiLoader />{t('loading.session', 'Chargement de la session...')}</LoadingContainer>;
  }

  if (error) {
    return (
      <PageContainer>
        <BackButton onClick={() => navigate(-1)}><FiChevronLeft /></BackButton>
        <ErrorMessage>{error}</ErrorMessage>
      </PageContainer>
    );
  }

  if (!session) {
    return (
      <PageContainer>
        <BackButton onClick={() => navigate(-1)}><FiChevronLeft /></BackButton>
        <ErrorMessage>{t('errors.sessionNotFound', 'Session non trouvée.')}</ErrorMessage>
      </PageContainer>
    );
  }

  // Construction de initialConfig pour AudioConfigPanel
  // Utilise les états actuels (qui ont été initialisés depuis localStorage ou session defaults)
  const audioPanelInitialConfig: SessionAudioConfig = {
    enableMusic,
    music: { url: musicFileUrl || session.audio?.music?.url || '', volume: musicVolume },
    enableAmbient,
    ambient: { url: ambientFileUrl || session.audio?.ambient?.url || '', volume: ambientVolume },
    enableBinaural,
    binaural: {
      baseFreq,
      beatFreq,
      volume: binauralVolume,
      // Si AudioConfigPanel peut utiliser une URL pour les binauraux pré-enregistrés et que vous la stockez:
      // url: session.audio?.binaural?.url,
    },
    voice: {
      volume: voiceVolume,
      // gender: voiceGender === 'auto' ? undefined : voiceGender,
    },
  };

  return (
    <PageContainer>
      <BackButton onClick={() => navigate(-1)} title={t('actions.back', "Retour") || "Retour"}>
        <FiChevronLeft />
      </BackButton>

      <HeaderImage imageUrl={session.imageUrl}>
        <HeaderContent>
          <h1>{session.title}</h1>
          <SessionInfoBar>
            <span><FiClock /> {session.estimatedDuration || session.durationMinutes || t('units.notAvailable', 'N/A')} {session.estimatedDuration || session.durationMinutes ? t('units.minutes', 'min') : ''}</span>
            <span><FiHeart /> {session.type.charAt(0).toUpperCase() + session.type.slice(1)}</span>
            {session.rating != null && <span><FiStar style={{color: '#FFC107'}}/> {session.rating.toFixed(1)}</span>}
          </SessionInfoBar>
        </HeaderContent>
      </HeaderImage>

      <ContentWrapper>
        <Section>
          <h2><FiInfo /> {t('sessionDetails.description', 'Description')}</h2>
          <p>{session.description}</p>
        </Section>

        {benefitsArray.length > 0 && (
          <Section>
            <h2><FiStar /> {t('sessionDetails.expectedBenefits', 'Bénéfices Attendus')}</h2>
            <ul>
              {benefitsArray.map((benefit: string, index: number) => (
                <li key={index}>{benefit}</li>
              ))}
            </ul>
          </Section>
        )}

        {(session.tags && session.tags.length > 0) && (
          <Section>
            <h2><FiTag /> {t('sessionDetails.keywords', 'Mots-clés')}</h2>
            <TagsContainer>
              {(session.tags || []).map((tag: string, index: number) => (
                <span key={index}>#{tag}</span>
              ))}
            </TagsContainer>
          </Section>
        )}

        <Section>
          <h2><FiSettings /> {t('sessionDetails.audioConfigGlobal', 'Configuration Audio de la Séance')}</h2>

          {/* Section "Ne Pas Déranger" - Conservée ici */}
          <AudioToggleGroup>
            <label htmlFor="toggle-dnd">
              {enableDND ? <FiBellOff /> : <FiBell />}
              {t('sessionDetails.dnd.label', 'Ne Pas Déranger (Application)')}
            </label>
            <div className="toggle-switch">
              <input id="toggle-dnd" type="checkbox" checked={enableDND} onChange={handleToggleDND} />
              <span className="slider"></span>
            </div>
          </AudioToggleGroup>
          {enableDND && notificationPermissionStatus === 'default' && (
            <InfoTextSmall>{t('sessionDetails.dnd.permissionNeededInfo', "Activer demandera la permission pour les notifications afin d'optimiser ce mode.")}</InfoTextSmall>
          )}
          {enableDND && notificationPermissionStatus === 'denied' && (
            <InfoTextSmall>{t('sessionDetails.dnd.permissionDeniedWarning', "Permission de notification refusée. Le mode NPD de l'application est actif, mais les notifications système ne sont pas affectées.")}</InfoTextSmall>
          )}
          {/* Fin de la Section "Ne Pas Déranger" */}

          {/* Intégration de AudioConfigPanel */}
          {/* On s'assure que la session et les états audio sont prêts avant de rendre AudioConfigPanel */}
          {!isLoading && session && (
            <AudioConfigPanel
              initialConfig={audioPanelInitialConfig}
              onConfigChange={handleAudioConfigChange}
            />
          )}
        </Section>

        {(session.comments && session.comments.length > 0) && (
          <Section>
            <h2><FiMessageSquare /> {t('sessionDetails.userReviews', 'Avis des Utilisateurs')}</h2>
            <CommentList>
              {(session.comments || []).map((comment: string, index: number) => (
                <li key={index}>{comment}</li>
              ))}
            </CommentList>
          </Section>
        )}

        {sessionId && (
            <Section id="journal">
                <h2><FiEdit3 /> {t('sessionDetails.yourNotes', 'Votre Journal pour cette Séance')}</h2>
                <JournalEntryForm sessionId={sessionId} onSave={handleSaveJournalNote} />
                {journalNotes.length > 0 && (
                <div style={{marginTop: '1rem'}}>
                    <h4>{t('sessionDetails.previousNotes', 'Notes précédentes')}:</h4>
                    <CommentList>
                    {journalNotes.map((note: string, index: number) => (
                        <li key={index}>{note}</li>
                    ))}
                    </CommentList>
                </div>
                )}
            </Section>
        )}
      </ContentWrapper>

      {session && (
        <PlayButtonFloating onClick={handlePlaySession} title={t('actions.startSession', "Lancer la session") || "Lancer la session"}>
          <FiPlayCircle />
        </PlayButtonFloating>
      )}
    </PageContainer>
  );
};

export default SessionDetailPage;