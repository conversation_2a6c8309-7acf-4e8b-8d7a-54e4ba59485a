// src/pages/LeaderboardPage.tsx

import React, { useEffect, useState } from 'react';
import styled, { css, keyframes } from 'styled-components';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { <PERSON>Award, FiUsers, FiChevronUp, FiChevronDown, FiMinus, FiLoader, FiShield } from 'react-icons/fi';

// Interface pour une entrée du leaderboard
interface LeaderboardEntry {
  rank: number;
  pseudo: string; // Sera anonymisé
  score: number;
  // Ajoutez d'autres champs si nécessaire, ex: niveau, avatar simplifié, etc.
  // Peut-être une indication si c'est l'utilisateur actuel
  isCurrentUser?: boolean; 
}

// --- Styled Components ---

const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 700px; /* Un peu plus resserré pour un leaderboard */
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    svg {
      opacity: 0.9;
    }
  }
  p {
    font-size: 1rem;
    color: ${({ theme }) => theme.textSecondary};
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.6;
  }
`;

const LeaderboardTable = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  overflow: hidden; /* Pour les coins arrondis avec le header */
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 60px 1fr 100px; /* Rang, Pseudo, Score */
  align-items: center;
  padding: 1rem 1.5rem;
  background: ${({ theme }) => theme.surfaceAlt};
  border-bottom: 1px solid ${({ theme }) => theme.border};
  font-weight: 600;
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const RankHeader = styled.div`text-align: center;`;
const PseudoHeader = styled.div``;
const ScoreHeader = styled.div`text-align: right;`;

const EntryRow = styled.div<{ $isCurrentUser?: boolean, $rank: number }>`
  display: grid;
  grid-template-columns: 60px 1fr 100px; /* Doit correspondre à TableHeader */
  align-items: center;
  padding: 1.2rem 1.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.border}80; // Bordure plus légère
  background: ${({ theme, $isCurrentUser }) => $isCurrentUser ? `${theme.primary}20` : 'transparent'};
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }
  &:hover {
    background-color: ${({ theme }) => theme.surfaceAlt};
  }
`;

const RankCell = styled.div<{ $rank: number }>`
  font-weight: 700;
  font-size: 1.1rem;
  color: ${({ theme, $rank }) => 
    $rank === 1 ? '#FFD700' : // Or
    $rank === 2 ? '#C0C0C0' : // Argent
    $rank === 3 ? '#CD7F32' : // Bronze
    theme.textSecondary
  };
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;

  svg { // Pour les icônes de rang
    font-size: 1.2em;
  }
`;

const PseudoCell = styled.div<{ $isCurrentUser?: boolean }>`
  font-weight: ${({ $isCurrentUser }) => $isCurrentUser ? '600' : '500'};
  color: ${({ theme, $isCurrentUser }) => $isCurrentUser ? theme.primary : theme.text};
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .avatar-placeholder { // Si vous ajoutez des avatars plus tard
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: ${({ theme }) => theme.textMuted}50;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
  }
`;

const ScoreCell = styled.div`
  font-weight: 600;
  font-size: 1.1rem;
  color: ${({ theme }) => theme.primary};
  text-align: right;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.primary};
  min-height: 70vh; /* Occupe plus de place */
  svg { font-size: 3.5rem; margin-bottom: 1.5rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.errorColor || 'red'};
  text-align: center;
  padding: 2rem;
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.errorColor || 'red'}33;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-height: 50vh;
  justify-content: center;

  p { margin-bottom: 1rem; }
  a {
    color: ${({ theme }) => theme.primary};
    text-decoration: underline;
    font-weight: 500;
  }
`;

const InfoMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: ${({ theme }) => theme.textSecondary};
  background-color: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  font-style: italic;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  svg {
    font-size: 2.5rem;
    opacity: 0.7;
  }
`;


// Simuler un fetch de données de leaderboard
// Dans une vraie application, cela viendrait de votre backend/Firebase
const fetchLeaderboardData = async (currentUserPseudo?: string): Promise<LeaderboardEntry[]> => {
  console.log("Fetching leaderboard data...");
  return new Promise(resolve => {
    setTimeout(() => {
      // Générer des pseudos anonymes plus variés
      const generatePseudo = (i: number) => `Utilisateur ${String.fromCharCode(65 + (i % 26))}${i}`;
      
      const data: LeaderboardEntry[] = Array.from({ length: 15 }, (_, i) => ({
        rank: i + 1,
        pseudo: generatePseudo(i), // Pseudos anonymisés
        score: Math.floor(Math.random() * 500) + 50, // Score aléatoire
        isCurrentUser: currentUserPseudo ? generatePseudo(i) === currentUserPseudo : (i === 5) // Simuler l'utilisateur actuel
      })).sort((a, b) => b.score - a.score) // Trier par score décroissant
         .map((entry, index) => ({ ...entry, rank: index + 1 })); // Réassigner le rang après le tri

      resolve(data);
    }, 1000); // Simuler une latence réseau
  });
};


const LeaderboardPage: React.FC = () => {
  const { t } = useTranslation();
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simuler un pseudo pour l'utilisateur actuel (vous le récupéreriez de l'authentification)
  const currentUserPseudo = "Utilisateur F"; // Exemple

  useEffect(() => {
    const loadLeaderboard = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await fetchLeaderboardData(currentUserPseudo);
        setLeaderboardData(data);
      } catch (err) {
        console.error("Error loading leaderboard:", err);
        setError(err instanceof Error ? err.message : t('errors.cantLoadLeaderboard', "Impossible de charger le classement."));
      } finally {
        setIsLoading(false);
      }
    };
    loadLeaderboard();
  }, [t, currentUserPseudo]); // currentUserPseudo dans les dépendances si dynamique


  const getRankIcon = (rank: number) => {
    if (rank === 1) return <FiAward style={{ color: '#FFD700' }} />;
    if (rank === 2) return <FiAward style={{ color: '#C0C0C0' }} />;
    if (rank === 3) return <FiAward style={{ color: '#CD7F32' }} />;
    return null; // Ou un autre icône pour les autres rangs
  };

  if (isLoading) {
    return <LoadingContainer><FiLoader /> {t('loading.leaderboard', 'Chargement du classement...')}</LoadingContainer>;
  }

  if (error) {
    return <ErrorMessage><p>{error}</p><Link to="/">{t('actions.backToHome', "Retour à l'accueil")}</Link></ErrorMessage>;
  }

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiAward /> {t('leaderboard.title', 'Classement des Explorateurs')}</h1>
        <p>{t('leaderboard.description', 'Découvrez votre position parmi les membres actifs de la communauté PiKnowKyo. Le classement est basé sur l\'engagement et la progression (anonymisé pour la confidentialité).')}</p>
      </PageHeader>

      {leaderboardData.length === 0 ? (
        <InfoMessage>
          <FiUsers />
          {t('leaderboard.noData', 'Le classement n\'est pas encore disponible ou est en cours de calcul. Revenez bientôt !')}
        </InfoMessage>
      ) : (
        <LeaderboardTable>
          <TableHeader>
            <RankHeader>#</RankHeader>
            <PseudoHeader>{t('leaderboard.pseudo', 'Pseudo (Anonyme)')}</PseudoHeader>
            <ScoreHeader>{t('leaderboard.score', 'Score')}</ScoreHeader>
          </TableHeader>
          {leaderboardData.map((entry) => (
            <EntryRow key={entry.rank} $isCurrentUser={entry.isCurrentUser} $rank={entry.rank}>
              <RankCell $rank={entry.rank}>
                {getRankIcon(entry.rank)}
                {entry.rank}
              </RankCell>
              <PseudoCell $isCurrentUser={entry.isCurrentUser}>
                {/* <span className="avatar-placeholder"><FiUser /></span> Optionnel */}
                {entry.pseudo} {entry.isCurrentUser && ` (${t('leaderboard.you', 'Vous')})`}
              </PseudoCell>
              <ScoreCell>{entry.score} pts</ScoreCell>
            </EntryRow>
          ))}
        </LeaderboardTable>
      )}
       <InfoMessage style={{marginTop: '2rem', fontSize: '0.9rem'}}>
          <FiShield />
          {t('leaderboard.privacyNote', 'Votre vie privée est importante. Tous les pseudos sont anonymisés pour protéger votre identité. Votre participation au classement est facultative et peut être gérée dans vos paramètres de profil.')}
      </InfoMessage>
    </PageContainer>
  );
};

export default LeaderboardPage;