// src/pages/BlogPostCommentsPage.tsx

import React, { useEffect, useState, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useLang } from '../LangProvider';
// import { useAuth } from '../AuthContext'; // Pour vérifier si l'utilisateur est connecté
import { FiMessageSquare, FiSend, FiChevronLeft, FiLoader, FiAlertCircle, FiUser, FiClock } from 'react-icons/fi';
// Firebase imports (à décommenter et configurer)
// import { db } from '../services/firebase';
// import { doc, getDoc, collection, addDoc, query, where, orderBy, onSnapshot, serverTimestamp, Timestamp } from "firebase/firestore";

// --- Types (réutiliser ou adapter depuis BlogPage et models.ts) ---
interface BlogPost { // Peut être importé de models.ts si centralisé
  id: string;
  authorId: string;
  authorPseudo: string;
  content: string;
  category: string;
  tags: string[];
  createdAt: any;
  likes: string[];
  commentCount: number;
  //postId: string;
  // ... autres champs du post ...
}

interface BlogComment {
  id: string;
  postId: string; // implicite car récupéré pour un post spécifique
  authorId: string;
  authorPseudo: string;
  text: string;
  createdAt: any; // Timestamp Firebase
}

interface CurrentUser { // Placeholder
  uid: string;
  displayName?: string | null;
}

// --- Styled Components (similaires à BlogPage, à adapter) ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;
const BackButton = styled.button` /* ... (style du BackButton de SessionDetailPage) ... */ `;
const PostDetailCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 1.5rem;
  margin-bottom: 2rem;
`;
const PostContent = styled.p` /* ... (style de BlogPage) ... */ `;
const AuthorInfo = styled.div` /* ... (style pour l'auteur et la date) ... */ `;

const CommentsSection = styled.section`
  margin-top: 2rem;
  h2 {
    font-size: 1.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid ${({ theme }) => theme.border};
  }
`;
const CommentList = styled.ul`
  list-style: none;
  padding: 0;
`;
const CommentItem = styled.li`
  background: ${({ theme }) => theme.surfaceAlt};
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 3px solid ${({ theme }) => theme.accent};
  p { margin: 0.5rem 0 0 0; font-size: 0.95rem; line-height: 1.6; }
  small { font-size: 0.8rem; color: ${({ theme }) => theme.textMuted}; }
`;

const CommentFormCard = styled.div` /* ... (style de PostFormCard de BlogPage) ... */ `;
const TextArea = styled.textarea` /* ... (style de BlogPage) ... */ `;
const Button = styled.button` /* ... (style de BlogPage) ... */ `;

const LoadingContainer = styled.div` /* ... */ `;
const ErrorMessage = styled.div` /* ... */ `;


const BlogPostCommentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { postId } = useParams<{ postId: string }>(); // Récupérer postId de l'URL
  const navigate = useNavigate();
  const { lang } = useLang();
  // const { currentUser } = useAuth();
  const currentUser: CurrentUser | null = { uid: "testUser123", displayName: "Moi" }; // Placeholder

  const [post, setPost] = useState<BlogPost | null>(null);
  const [comments, setComments] = useState<BlogComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoadingPost, setIsLoadingPost] = useState(true);
  const [isLoadingComments, setIsLoadingComments] = useState(true);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Charger les détails du post
  useEffect(() => {
    if (!postId) {
      setError(t('errors.missingPostId', "ID du message manquant."));
      setIsLoadingPost(false);
      return;
    }
    setIsLoadingPost(true);
    // SIMULATION (remplacer par fetch Firebase)
    // const postRef = doc(db, "blogPosts", postId);
    // getDoc(postRef).then(docSnap => {
    //   if (docSnap.exists()) {
    //     setPost({ id: docSnap.id, ...docSnap.data() } as BlogPost);
    //   } else {
    //     setError(t('errors.postNotFound', "Message non trouvé."));
    //   }
    // }).catch(err => {
    //   console.error("Error fetching post:", err);
    //   setError(t('errors.cantLoadPost', "Impossible de charger le message."));
    // }).finally(() => setIsLoadingPost(false));

    // Simulation
    setTimeout(() => {
        // Simulation des données du post
        const fakePostData: BlogPost = {
          id: postId, // L'id du post est postId (venant de l'URL)
          authorId: 'uidX',
          authorPseudo: t('blog.sampleAuthor', 'Auteur Anonyme'),
          content: t('blog.samplePostContent', `Contenu détaillé du message avec l'ID ${postId}. Ce message parle de l'importance de la pleine conscience dans notre quotidien stressant et comment de simples exercices peuvent apporter une grande paix intérieure.`),
          category: 'général',
          tags: ['exemple'],
          createdAt: { seconds: Date.now()/1000 - 86400, nanoseconds: 0 }, // Simuler un timestamp
          likes: ['uidTest1', 'uidTest2'],
          commentCount: 2//,
          //language: lang, // Optionnel, mais bien de l'avoir si votre type le définit
          // imageUrl: `/images/blog/placeholder_image_for_${postId}.webp` // Exemple d'URL d'image
        };
        setPost(fakePostData);
        setIsLoadingPost(false);
      }, 500);

  }, [postId, t]);

  // Charger/Écouter les commentaires pour ce post
  useEffect(() => {
    if (!postId) return;
    setIsLoadingComments(true);
    // SIMULATION (remplacer par listener Firebase)
    // const commentsQuery = query(collection(db, "blogComments"), where("postId", "==", postId), orderBy("createdAt", "asc"));
    // const unsubscribe = onSnapshot(commentsQuery, (snapshot) => {
    //   const fetchedComments = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BlogComment));
    //   setComments(fetchedComments);
    //   setIsLoadingComments(false);
    // }, (err) => {
    //   console.error("Error fetching comments:", err);
    //   setError(t('errors.cantLoadComments', "Impossible de charger les commentaires."));
    //   setIsLoadingComments(false);
    // });
    // return () => unsubscribe();

    // Simulation
    setTimeout(() => {
        const fakeComments: BlogComment[] = [
            {id: 'c1', postId: postId, authorId: 'uidY', authorPseudo: t('blog.sampleCommenter1', 'Commentateur1'), text: t('blog.sampleComment1', 'Super message !'), createdAt: {seconds: Date.now()/1000 - 3600}},
            {id: 'c2', postId: postId, authorId: 'uidZ', authorPseudo: t('blog.sampleCommenter2', 'AutrePersonne'), text: t('blog.sampleComment2', 'Très intéressant, merci du partage.'), createdAt: {seconds: Date.now()/1000 - 1800}},
        ];
        setComments(fakeComments);
        setIsLoadingComments(false);
    }, 800);

  }, [postId, t]);

  const handleCommentSubmit = async () => {
    if (!newComment.trim() || !currentUser || !postId) return;
    setIsSubmittingComment(true);
    const commentData = {
      postId: postId,
      authorId: currentUser.uid,
      authorPseudo: t('blog.anonymousUser', 'Utilisateur Anonyme'), // Ou currentUser.displayName si vous ne voulez pas anonymiser les commentaires
      text: newComment,
      // createdAt: serverTimestamp(), // Pour Firebase
    };
    try {
      // await addDoc(collection(db, "blogComments"), commentData);
      // Mettre à jour le compteur de commentaires sur le post (transaction Firebase)
      console.log("Nouveau commentaire (simulation):", commentData);
      // Pour la démo, on l'ajoute localement
      setComments(prev => [...prev, {...commentData, id: Date.now().toString(), createdAt: {seconds: Date.now()/1000}} as BlogComment]);
      setNewComment('');
    } catch (error) {
      console.error("Error adding comment:", error);
      alert(t('errors.cantAddComment', "Erreur lors de l'ajout du commentaire."));
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const formatDate = (timestamp: any) => { /* ... (copier depuis BlogPage) ... */ return new Date(timestamp.seconds * 1000).toLocaleDateString(); };


  if (isLoadingPost) return <LoadingContainer><FiLoader /> {t('loading.post', 'Chargement du message...')}</LoadingContainer>;
  if (error) return <ErrorMessage><p>{error}</p><Link to="/blog">{t('actions.backToBlog', 'Retour au blog')}</Link></ErrorMessage>;
  if (!post) return <ErrorMessage><p>{t('errors.postNotFound', 'Message non trouvé.')}</p><Link to="/blog">{t('actions.backToBlog', 'Retour au blog')}</Link></ErrorMessage>;

  return (
    <PageContainer>
      <BackButton onClick={() => navigate('/blog')} title={t('actions.backToBlog', "Retour au blog") || "Retour au blog"}>
        <FiChevronLeft />
      </BackButton>

      <PostDetailCard>
        <AuthorInfo>
            <span><FiUser /> {post.authorPseudo}</span>
            <span><FiClock /> {formatDate(post.createdAt)}</span>
        </AuthorInfo>
        <PostContent>{post.content}</PostContent>
        {/* On pourrait afficher les tags ici aussi */}
      </PostDetailCard>

      <CommentsSection>
        <h2>{t('blog.commentsSectionTitle', 'Commentaires')} ({comments.length})</h2>
        {isLoadingComments && <LoadingContainer><FiLoader /> {t('loading.comments', 'Chargement des commentaires...')}</LoadingContainer>}

        {!isLoadingComments && comments.length === 0 && (
          <p>{t('blog.noCommentsYet', 'Aucun commentaire pour le moment. Soyez le premier à commenter !')}</p>
        )}

        <CommentList>
          {comments.map(comment => (
            <CommentItem key={comment.id}>
              <small><strong>{comment.authorPseudo}</strong> - {formatDate(comment.createdAt)}</small>
              <p>{comment.text}</p>
            </CommentItem>
          ))}
        </CommentList>

        {currentUser ? (
          <CommentFormCard style={{marginTop: '2rem'}}>
            <h3>{t('blog.addComment', 'Ajouter un commentaire')}</h3>
            <TextArea
              rows={3}
              placeholder={t('blog.yourCommentPlaceholder', 'Votre commentaire...') || "Votre commentaire..."}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
            <Button onClick={handleCommentSubmit} disabled={isSubmittingComment || !newComment.trim()}>
              {isSubmittingComment ? <FiLoader style={{animation: 'spin 1s linear infinite'}}/> : <FiSend />}
              {isSubmittingComment ? t('blog.sending', 'Envoi...') : t('blog.sendComment', 'Envoyer')}
            </Button>
          </CommentFormCard>
        ) : (
          <p style={{marginTop: '2rem', textAlign: 'center'}}>
            {t('blog.loginToComment', 'Connectez-vous pour ajouter un commentaire.')}
          </p>
        )}
      </CommentsSection>
    </PageContainer>
  );
};

export default BlogPostCommentsPage;