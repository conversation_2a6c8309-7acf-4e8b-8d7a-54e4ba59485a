// Re-export des types de l'application principale
export interface SessionAudioConfig {
  music?: {
    url: string;
    volume?: number;
  };
  ambient?: {
    url: string;
    volume?: number;
  };
  binaural?: {
    volume?: number;
    baseFreq?: number;
    beatFreq?: number;
  };
  voice?: {
    volume?: number;
    gender?: 'masculine' | 'feminine' | 'neutral';
  };
  enableMusic?: boolean;
  enableAmbient?: boolean;
  enableBinaural?: boolean;
}

export interface AudioAsset {
  id: string;
  name: string;
  url: string;
  type: 'music' | 'ambient';
  isUserUploaded?: boolean;
  userId?: string;
  storagePath?: string;
  size?: number;
  createdAt?: Date;
}

export interface ScriptLine {
  text: string;
  duration?: number;
  pause?: number;
  speaker?: string;
  rate?: number;
  pitch?: number;
}

export interface Session {
  id: string;
  title: string;
  description: string;
  type: string;
  category: string;
  language: string;
  imageUrl?: string;
  durationMinutes?: number;
  estimatedDuration?: number;
  rating?: number;
  tags?: string[];
  benefits?: string[];
  comments?: string[];
  audio?: SessionAudioConfig;
  script?: ScriptLine[];
}

export type SubscriptionPlan = 'free' | 'premium';

export interface TemporaryUnlock {
  type: 'session' | 'game';
  expiresAt: number;
  sessionId?: string;
}

export interface MonetizationState {
  currentPlan: SubscriptionPlan;
  premiumPrice: number;
  premiumCurrency: string;
  temporaryUnlocks: TemporaryUnlock[];
  isProcessingPayment: boolean;
}

// Re-export des types admin
export * from './admin';
