import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  ToggleOn,
  ToggleOff,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import toast from 'react-hot-toast';
import { SubscriptionPricing } from '@/types';
import pricingService from '@/services/pricingService';

// Schéma de validation
const pricingSchema = yup.object({
  planName: yup.string().required('Le nom du plan est requis'),
  price: yup.number().positive('Le prix doit être positif').required('Le prix est requis'),
  currency: yup.string().required('La devise est requise'),
  duration: yup.string().oneOf(['monthly', 'yearly']).required('La durée est requise'),
  features: yup.array().of(yup.string()).min(1, 'Au moins une fonctionnalité est requise'),
  isActive: yup.boolean(),
});

type PricingFormData = yup.InferType<typeof pricingSchema>;

const Pricing: React.FC = () => {
  const [plans, setPlans] = useState<SubscriptionPricing[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<SubscriptionPricing | null>(null);
  const [featureInput, setFeatureInput] = useState('');

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<PricingFormData>({
    resolver: yupResolver(pricingSchema),
    defaultValues: {
      planName: '',
      price: 0,
      currency: 'EUR',
      duration: 'monthly',
      features: [],
      isActive: true,
    },
  });

  const watchedFeatures = watch('features') || [];

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      const data = await pricingService.getAllPricingPlans();
      setPlans(data);
    } catch (error) {
      toast.error('Erreur lors du chargement des plans');
      console.error('Erreur:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (plan?: SubscriptionPricing) => {
    if (plan) {
      setEditingPlan(plan);
      reset({
        planName: plan.planName,
        price: plan.price,
        currency: plan.currency,
        duration: plan.duration,
        features: plan.features,
        isActive: plan.isActive,
      });
    } else {
      setEditingPlan(null);
      reset({
        planName: '',
        price: 0,
        currency: 'EUR',
        duration: 'monthly',
        features: [],
        isActive: true,
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingPlan(null);
    setFeatureInput('');
  };

  const onSubmit = async (data: PricingFormData) => {
    try {
      if (editingPlan) {
        await pricingService.updatePricingPlan(editingPlan.id, data);
        toast.success('Plan mis à jour avec succès');
      } else {
        await pricingService.createPricingPlan(data);
        toast.success('Plan créé avec succès');
      }
      
      await loadPlans();
      handleCloseDialog();
    } catch (error) {
      toast.error('Erreur lors de la sauvegarde');
      console.error('Erreur:', error);
    }
  };

  const handleDeletePlan = async (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce plan ?')) {
      try {
        await pricingService.deletePricingPlan(id);
        toast.success('Plan supprimé avec succès');
        await loadPlans();
      } catch (error) {
        toast.error('Erreur lors de la suppression');
        console.error('Erreur:', error);
      }
    }
  };

  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      await pricingService.togglePlanStatus(id, !currentStatus);
      toast.success(`Plan ${!currentStatus ? 'activé' : 'désactivé'} avec succès`);
      await loadPlans();
    } catch (error) {
      toast.error('Erreur lors du changement de statut');
      console.error('Erreur:', error);
    }
  };

  const addFeature = () => {
    if (featureInput.trim()) {
      const currentFeatures = watchedFeatures;
      setValue('features', [...currentFeatures, featureInput.trim()]);
      setFeatureInput('');
    }
  };

  const removeFeature = (index: number) => {
    const currentFeatures = watchedFeatures;
    setValue('features', currentFeatures.filter((_, i) => i !== index));
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Gestion des Prix
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog()}
        >
          Nouveau Plan
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nom du Plan</TableCell>
              <TableCell>Prix</TableCell>
              <TableCell>Durée</TableCell>
              <TableCell>Fonctionnalités</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {plans.map((plan) => (
              <TableRow key={plan.id}>
                <TableCell>{plan.planName}</TableCell>
                <TableCell>
                  {plan.price} {plan.currency}
                </TableCell>
                <TableCell>
                  <Chip 
                    label={plan.duration === 'monthly' ? 'Mensuel' : 'Annuel'}
                    color={plan.duration === 'monthly' ? 'primary' : 'secondary'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {plan.features.slice(0, 3).map((feature, index) => (
                      <Chip key={index} label={feature} size="small" variant="outlined" />
                    ))}
                    {plan.features.length > 3 && (
                      <Chip label={`+${plan.features.length - 3}`} size="small" />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={plan.isActive ? 'Actif' : 'Inactif'}
                    color={plan.isActive ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton
                    onClick={() => handleToggleStatus(plan.id, plan.isActive)}
                    color={plan.isActive ? 'warning' : 'success'}
                  >
                    {plan.isActive ? <ToggleOff /> : <ToggleOn />}
                  </IconButton>
                  <IconButton
                    onClick={() => handleOpenDialog(plan)}
                    color="primary"
                  >
                    <Edit />
                  </IconButton>
                  <IconButton
                    onClick={() => handleDeletePlan(plan.id)}
                    color="error"
                  >
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog pour créer/éditer un plan */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogTitle>
            {editingPlan ? 'Modifier le Plan' : 'Nouveau Plan'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <Controller
                name="planName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Nom du Plan"
                    fullWidth
                    error={!!errors.planName}
                    helperText={errors.planName?.message}
                  />
                )}
              />

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Controller
                  name="price"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Prix"
                      type="number"
                      fullWidth
                      error={!!errors.price}
                      helperText={errors.price?.message}
                    />
                  )}
                />

                <Controller
                  name="currency"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Devise</InputLabel>
                      <Select {...field} label="Devise">
                        <MenuItem value="EUR">EUR</MenuItem>
                        <MenuItem value="USD">USD</MenuItem>
                        <MenuItem value="GBP">GBP</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />

                <Controller
                  name="duration"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Durée</InputLabel>
                      <Select {...field} label="Durée">
                        <MenuItem value="monthly">Mensuel</MenuItem>
                        <MenuItem value="yearly">Annuel</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Box>

              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Fonctionnalités
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                  <TextField
                    label="Ajouter une fonctionnalité"
                    value={featureInput}
                    onChange={(e) => setFeatureInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                    fullWidth
                  />
                  <Button onClick={addFeature} variant="outlined">
                    Ajouter
                  </Button>
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {watchedFeatures.map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      onDelete={() => removeFeature(index)}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
                {errors.features && (
                  <Alert severity="error" sx={{ mt: 1 }}>
                    {errors.features.message}
                  </Alert>
                )}
              </Box>

              <Controller
                name="isActive"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Switch {...field} checked={field.value} />}
                    label="Plan actif"
                  />
                )}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annuler</Button>
            <Button 
              type="submit" 
              variant="contained"
              disabled={isSubmitting}
            >
              {isSubmitting ? <CircularProgress size={24} /> : 'Sauvegarder'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default Pricing;
