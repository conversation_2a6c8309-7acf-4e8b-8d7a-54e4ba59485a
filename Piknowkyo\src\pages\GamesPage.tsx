// src/pages/GamesPage.tsx

import React, { useState, useEffect, useCallback, useContext } from 'react';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { FiPlay, FiInfo, FiAward, FiStar, FiClock, FiGrid, FiZap, FiLock } from 'react-icons/fi';
import { GameInfo, GameProps, SavedGameState } from '../games/common/models';
import { loadGameState, submitScoreToLeaderboard, getPersonalBestScore } from '../games/common/gameUtils';
import ReusableModal from '../components/ReusableModal';
import MonetizationModal from '../components/MonetizationModal';
import UnlockTimer from '../components/UnlockTimer';
import { useAppSelector } from '../store/hooks';
import { MonetizationService } from '../services/monetizationService';

// CORRECTION : Déplacer tous les imports en haut du fichier
import ZenTetrisGame from '../games/zen-tetris/GameComponent'; // Exemple: Zen Tetris
// import MemoryChallengeGame from '../games/memory-challenge/GameComponent'; // Autre jeu

// IMPORTANT: Vous devez implémenter votre AuthContext et fournir un userId
// Pour l'exemple, j'ai inclus un placeholder simple.
// Si vous n'avez pas de système d'authentification, userId sera "guest"
const useAuth = () => ({ userId: "guest_user_123" }); // Cette déclaration doit rester après tous les imports

// --- Styled Components spécifiques à GamesPage ---
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageTitle = styled.h1`
  text-align: center;
  color: ${({ theme }) => theme.primary};
  margin-bottom: 1rem;
  font-size: 2rem;
`;

const GameGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
`;

const GameCardWrapper = styled.div<{ $canAccess: boolean }>`
  position: relative;
  cursor: ${({ $canAccess }) => $canAccess ? 'pointer' : 'default'};
  transition: all 0.3s ease;

  &:hover {
    transform: ${({ $canAccess }) => $canAccess ? 'translateY(-4px)' : 'none'};
  }
`;

const GameCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  overflow: hidden;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.cardHoverShadow || '0 6px 18px rgba(0,0,0,0.1)'};
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::after {
    opacity: 1;
  }
`;

const GameThumbnail = styled.div<{ $imageUrl?: string }>`
  height: 150px;
  background: ${({ $imageUrl, theme }) =>
    $imageUrl
      ? `url(${$imageUrl}) center/cover`
      : `linear-gradient(135deg, ${theme.primary}, ${theme.secondary})`
  };
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const GameIcon = styled.div`
  font-size: 3rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
`;

const CardContent = styled.div`
  padding: 1rem;
`;

const CardTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  color: ${({ theme }) => theme.primary};
  font-size: 1.2rem;
  line-height: 1.3;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CardDescription = styled.p`
  color: ${({ theme }) => theme.textSecondary};
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
`;

const GameStats = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: ${({ theme }) => theme.textMuted};

  span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
`;

const CardFooter = styled.div`
  padding: 0 1rem 1rem 1rem;
  display: flex;
  gap: 0.5rem;
`;

const PlayButton = styled.button`
  flex: 1;
  background: ${({ theme }) => theme.primary};
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover:not(:disabled) {
    background: ${({ theme }) => theme.primaryDark || theme.primary};
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const InfoButton = styled.button`
  background: ${({ theme }) => theme.secondary};
  color: ${({ theme }) => theme.text};
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: ${({ theme }) => theme.secondaryDark || theme.secondary};
  }
`;

const GameEndModal = styled.div`
  text-align: center;

  h3 {
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
  }

  .score {
    font-size: 1.5rem;
    font-weight: bold;
    color: ${({ theme }) => theme.accent};
    margin: 1rem 0;
  }

  .personal-best {
    color: ${({ theme }) => theme.success || theme.primary};
    font-weight: 600;
  }
`;

const BackToGamesButton = styled.button`
  background: ${({ theme }) => theme.primary};
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem auto 0;

  &:hover {
    background: ${({ theme }) => theme.primaryDark || theme.primary};
    transform: translateY(-1px);
  }
`;

const GameRestrictedOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.95));
    transform: scale(1.02);
  }

  .lock-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    opacity: 0.9;
    animation: pulse 2s infinite;
  }

  .unlock-text {
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
  }

  .cta-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
    max-width: 200px;
  }

  @keyframes pulse {
    0%, 100% { opacity: 0.9; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
  }
`;

const GameCTAButton = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  ${({ $variant, theme }) => $variant === 'primary' ? `
    background: ${theme.primary};
    color: white;

    &:hover {
      background: ${theme.primaryDark || theme.primary};
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }
  ` : `
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  `}
`;

const GamePlayButton = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: ${({ theme }) => theme.primary};
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);

  ${GameCard}:hover & {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
`;

// --- Liste des jeux disponibles ---
const GAMES_LIST: GameInfo[] = [
  {
    id: "zen-tetris",
    titleKey: "games.zenTetris.title",
    descriptionKey: "games.zenTetris.description",
    thumbnailUrl: "/assets/images/games/zen_tetris_thumbnail.jpg",
    component: ZenTetrisGame,
    maxLevels: 10,
    estimatedDurationMinutes: 10,
    tags: ["relaxation", "logique"],
    icon: FiGrid
  },
  // {
  //   id: "memory-challenge",
  //   titleKey: "games.memoryChallenge.title",
  //   descriptionKey: "games.memoryChallenge.description",
  //   thumbnailUrl: "/assets/images/games/memory_challenge_thumbnail.jpg",
  //   component: MemoryChallengeGame,
  //   maxLevels: 100,
  //   estimatedDurationMinutes: 5,
  //   tags: ["mémoire", "logique"]
  // },
  // ... ajoutez d'autres jeux ici
];

const GamesPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const { userId } = useAuth(); // Obtenez l'ID utilisateur via votre contexte d'authentification

  // Redux state pour la monétisation
  const { currentPlan, temporaryUnlocks } = useAppSelector(state => state.monetization);

  const [activeGameInfo, setActiveGameInfo] = useState<GameInfo | null>(null);
  const [initialGameLoadState, setInitialGameLoadState] = useState<SavedGameState | null>(null);

  const [showInfoModal, setShowInfoModal] = useState(false);
  const [selectedGameForInfo, setSelectedGameForInfo] = useState<GameInfo | null>(null);
  const [showMonetizationModal, setShowMonetizationModal] = useState(false);

  const [currentPersonalBest, setCurrentPersonalBest] = useState(0);

  // Charger la meilleure progression personnelle quand la modale info s'ouvre
  useEffect(() => {
    if (selectedGameForInfo && userId) {
      const best = getPersonalBestScore(selectedGameForInfo.id, userId);
      setCurrentPersonalBest(best);
    }
  }, [selectedGameForInfo, userId]);

  const handlePlayGame = useCallback((game: GameInfo) => {
    // Vérifier les restrictions de monétisation
    const canAccess = MonetizationService.canAccessGame(game.id, currentPlan, temporaryUnlocks);

    if (!canAccess) {
      setShowMonetizationModal(true);
      return;
    }

    // Vérifier si une partie est sauvegardée
    const savedState = userId ? loadGameState(game.id, userId) : null;
    if (savedState) {
      setInitialGameLoadState(savedState);
    } else {
      setInitialGameLoadState(null); // Pas de sauvegarde, commencer frais
    }
    setActiveGameInfo(game); // Lance le jeu en affichant son composant
  }, [userId, currentPlan, temporaryUnlocks]);

  // Fonctions de gestion des clics pour les jeux
  const handleGameClick = (game: GameInfo, canAccess: boolean) => {
    if (canAccess) {
      handlePlayGame(game);
    } else {
      setShowMonetizationModal(true);
    }
  };

  const handleWatchAdClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMonetizationModal(true);
  };

  const handleUpgradeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate('/monetization');
  };

  const handleGameEnd = useCallback((finalScore: number) => {
    // Le jeu est terminé, soumettre le score sera géré par le jeu lui-même maintenant
    // Le jeu appelera submitScoreToLeaderboard directement si la partie est finie.
    // Ici, on gère juste l'affichage post-game.

    console.log(`Jeu terminé avec un score de ${finalScore}`);

    setActiveGameInfo(null); // Revenir à la liste des jeux
  }, [activeGameInfo]); // userId n'est plus une dépendance car submitScore est appelé par le jeu

  const handleBackToGames = useCallback(() => {
    setActiveGameInfo(null); // Revenir à la liste des jeux
    setInitialGameLoadState(null); // Effacer l'état initial chargé pour la prochaine fois
  }, []);

  const handleOpenInfoModal = (game: GameInfo) => {
    setSelectedGameForInfo(game);
    setShowInfoModal(true);
  };

  const handleCloseInfoModal = () => {
    setShowInfoModal(false);
    setSelectedGameForInfo(null);
  };

  // Rendu conditionnel du jeu ou de la liste des jeux
  if (activeGameInfo) {
    const GameComponent = activeGameInfo.component;
    return (
      <GameComponent
        gameInfo={activeGameInfo}
        userId={userId || "guest"} // Utiliser "guest" si pas authentifié, mais la sauvegarde ne fonctionnera pas sans userId réel
        onGameEnd={handleGameEnd}
        onBackToGames={handleBackToGames}
        initialGameState={initialGameLoadState} // 'initialGameLoadState' peut être null, ce qui est maintenant accepté par GameProps
      />
    );
  }

  return (
    <PageContainer>
      <PageTitle>{t('games.title', 'Mini-Jeux de Développement Personnel')}</PageTitle>
      <p style={{textAlign: 'center', color: theme.textMuted}}>{t('games.intro', 'Testez et améliorez vos compétences avec nos mini-jeux amusants et stimulants.')}</p>

      {/* Afficher le timer d'unlock si actif */}
      {currentPlan === 'free' && temporaryUnlocks.some(unlock => unlock.type === 'game' && unlock.expiresAt > Date.now()) && (
        <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '1rem' }}>
          <UnlockTimer
            type="game"
            onExpired={() => {
              // Optionnel: afficher une notification ou rediriger
              alert(t('games.unlockExpired', 'Votre accès temporaire aux jeux a expiré.'));
            }}
            showExtendOption={true}
            onExtend={() => setShowMonetizationModal(true)}
          />
        </div>
      )}

      <GameGrid>
        {GAMES_LIST.map((game) => {
          const personalBest = userId ? getPersonalBestScore(game.id, userId) : 0;
          const savedGame = userId ? loadGameState(game.id, userId) : null;
          const canAccess = MonetizationService.canAccessGame(game.id, currentPlan, temporaryUnlocks);

          return (
            <GameCardWrapper
              key={game.id}
              $canAccess={canAccess}
              onClick={() => handleGameClick(game, canAccess)}
            >
              <GameCard>
                <GameThumbnail $imageUrl={game.thumbnailUrl}>
                  <GameIcon>
                    {game.icon && <game.icon />}
                  </GameIcon>
                  {canAccess && (
                    <GamePlayButton>
                      <FiPlay />
                    </GamePlayButton>
                  )}
                </GameThumbnail>
                <CardContent>
                  <CardTitle>
                    {t(game.titleKey)}
                    {!canAccess && (
                      <span style={{
                        background: theme.primary,
                        color: 'white',
                        fontSize: '0.7rem',
                        padding: '0.2rem 0.5rem',
                        borderRadius: '12px',
                        fontWeight: 600,
                        marginLeft: '0.5rem'
                      }}>
                        <FiStar size={10} style={{ marginRight: '0.25rem' }} />
                        Premium
                      </span>
                    )}
                  </CardTitle>
                  <CardDescription>{t(game.descriptionKey)}</CardDescription>
                  <GameStats>
                      {game.estimatedDurationMinutes && (
                          <span><FiClock /> {t('games.estimatedDuration', 'Durée estimée')}: {game.estimatedDurationMinutes} {t('units.minutes', 'min')}</span>
                      )}
                      {personalBest > 0 && (
                          <span><FiAward /> {t('games.personalBest', 'Record personnel')}: {personalBest} {t('units.points', 'pts')}</span>
                      )}
                      {savedGame && (
                          <span><FiStar style={{color: theme.accent}} /> {t('games.savedGameProgress', 'Partie sauvegardée')}: {savedGame.level}</span>
                      )}
                  </GameStats>
                </CardContent>
                <CardFooter>
                  <PlayButton
                    onClick={(e) => {
                      e.stopPropagation();
                      handleGameClick(game, canAccess);
                    }}
                    style={{
                      opacity: canAccess ? 1 : 0.6,
                      cursor: canAccess ? 'pointer' : 'not-allowed'
                    }}
                  >
                    {!canAccess ? (
                      <><FiLock /> {t('games.premiumRequired', 'Premium requis')}</>
                    ) : (
                      <><FiPlay /> {savedGame ? t('games.continueGame', 'Continuer la partie') : t('games.newGame', 'Nouvelle partie')}</>
                    )}
                  </PlayButton>
                  <InfoButton onClick={(e) => {
                    e.stopPropagation();
                    handleOpenInfoModal(game);
                  }}>
                    <FiInfo />
                  </InfoButton>
                </CardFooter>

                {!canAccess && (
                  <GameRestrictedOverlay onClick={(e) => { e.preventDefault(); handleGameClick(game, false); }}>
                    <FiLock className="lock-icon" />
                    <div className="unlock-text">
                      {t('games.unlockThisGame')}
                    </div>
                    <div className="cta-buttons">
                      <GameCTAButton
                        $variant="secondary"
                        onClick={handleWatchAdClick}
                      >
                        <FiPlay />
                        {t('games.watchAdCTA')}
                      </GameCTAButton>
                      <GameCTAButton
                        $variant="primary"
                        onClick={handleUpgradeClick}
                      >
                        <FiZap />
                        {t('games.upgradeCTA')}
                      </GameCTAButton>
                    </div>
                  </GameRestrictedOverlay>
                )}
              </GameCard>
            </GameCardWrapper>
          );
        })}
      </GameGrid>

      {/* Modale d'informations sur le jeu */}
      {selectedGameForInfo && (
        <ReusableModal
          isOpen={showInfoModal}
          onClose={handleCloseInfoModal}
          title={t(selectedGameForInfo.titleKey)}
          titleIcon={<FiInfo />}
          footerContent={
            <PlayButton onClick={() => { handlePlayGame(selectedGameForInfo); handleCloseInfoModal(); }}>
              <FiPlay /> {loadGameState(selectedGameForInfo.id, userId || "guest") ? t('games.continueGame', 'Continuer la partie') : t('games.newGame', 'Nouvelle partie')}
            </PlayButton>
          }
        >
          <p>{t(selectedGameForInfo.descriptionKey)}</p>
          <p>{t('games.maxLevels', 'Ce jeu contient {{maxLevels}} niveaux de difficulté.', { maxLevels: selectedGameForInfo.maxLevels })}</p>
          {userId && (
              <p>{t('games.yourBestScore', 'Votre meilleur score sur ce jeu est de {{score}} points.', { score: currentPersonalBest })}</p>
          )}
          {selectedGameForInfo.tags && selectedGameForInfo.tags.length > 0 && (
              <p>{t('games.keywords', 'Mots-clés')} : {selectedGameForInfo.tags.join(', ')}</p>
          )}
        </ReusableModal>
      )}

      {/* Modale de fin de partie */}
      {/* Cette modale n'est plus nécessaire car chaque jeu gère sa propre fin de partie */}

      {/* Modal de monétisation */}
      <MonetizationModal
        isOpen={showMonetizationModal}
        onClose={() => setShowMonetizationModal(false)}
        type="game"
        onUpgrade={() => {
          // Naviguer vers la page de monétisation
          navigate('/monetization');
        }}
      />

    </PageContainer>
  );
};

export default GamesPage;
