# Guide d'Installation - Piknowkyo Admin

Ce guide vous accompagne dans l'installation et la configuration du gestionnaire d'administration Piknowkyo.

## 📋 Prérequis

### Logiciels requis
- **Node.js** (version 18 ou supérieure)
- **npm** (inclus avec Node.js)
- **Git** (pour le versioning)

### Comptes et services
- **Compte Firebase** avec un projet configuré
- **Comptes API IA** (optionnel) :
  - Groq API
  - Mistral AI
  - Google AI (Gemini)
  - Chutes.ai

## 🚀 Installation rapide

### 1. Cloner et configurer
```bash
# Se placer dans le dossier Pmanager
cd Pmanager

# Exécuter le script de configuration automatique
./scripts/setup.sh
```

### 2. Configuration manuelle (alternative)

Si le script automatique ne fonctionne pas :

```bash
# Installer les dépendances
npm install

# Copier le fichier d'environnement
cp .env.example .env
```

## ⚙️ Configuration

### 1. Variables d'environnement

Éditez le fichier `.env` avec vos clés Firebase :

```env
# Configuration Firebase (obligatoire)
VITE_FIREBASE_API_KEY=votre_api_key
VITE_FIREBASE_AUTH_DOMAIN=votre_auth_domain
VITE_FIREBASE_PROJECT_ID=votre_project_id
VITE_FIREBASE_STORAGE_BUCKET=votre_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=votre_messaging_sender_id
VITE_FIREBASE_APP_ID=votre_app_id

# Développement (optionnel)
VITE_USE_FIREBASE_EMULATOR=false
```

### 2. Configuration Firebase

#### A. Authentification
1. Aller dans la console Firebase
2. Activer l'authentification par email/mot de passe
3. Activer l'authentification Google (optionnel)

#### B. Firestore
1. Créer une base de données Firestore
2. Déployer les règles de sécurité :
```bash
firebase deploy --only firestore:rules
```

#### C. Indexes Firestore
```bash
firebase deploy --only firestore:indexes
```

### 3. Initialisation des données

Exécutez le script d'initialisation pour créer le premier administrateur :

```bash
node scripts/init-admin.js
```

**Informations de connexion par défaut :**
- Email : `<EMAIL>`
- Mot de passe : `AdminPiknowkyo2024!`

⚠️ **Important** : Changez ces identifiants après la première connexion !

## 🏃‍♂️ Lancement

### Mode développement
```bash
npm run dev
```
L'application sera disponible sur `http://localhost:3001`

### Build de production
```bash
npm run build
```

### Déploiement
```bash
npm run deploy
```

## 🔧 Configuration avancée

### 1. Fournisseurs IA

Après la première connexion, configurez les fournisseurs IA dans l'interface :

1. Aller dans **IA & Scripts**
2. Configurer chaque fournisseur avec sa clé API
3. Tester la connexion
4. Activer les fournisseurs souhaités

### 2. Plans de tarification

Les plans par défaut sont créés automatiquement :
- **Gratuit** : 0€/mois
- **Premium Mensuel** : 9.99€/mois  
- **Premium Annuel** : 99.99€/an

Vous pouvez les modifier dans **Tarification**.

### 3. Gestion des utilisateurs

#### Créer un nouvel administrateur :
1. Aller dans **ACL**
2. Cliquer sur **Nouvel utilisateur**
3. Définir le rôle et les permissions
4. L'utilisateur recevra un email d'invitation

#### Rôles disponibles :
- **Super Admin** : Accès complet
- **Admin** : Gestion générale
- **Moderator** : Modération de contenu
- **Content Manager** : Gestion du contenu uniquement

## 🛡️ Sécurité

### Règles Firestore
Les règles de sécurité sont configurées pour :
- Vérifier l'authentification
- Contrôler les permissions par rôle
- Protéger les données sensibles

### Bonnes pratiques
1. **Changez les identifiants par défaut**
2. **Utilisez des mots de passe forts**
3. **Limitez les permissions au minimum nécessaire**
4. **Surveillez les logs d'accès**
5. **Mettez à jour régulièrement les dépendances**

## 🐛 Dépannage

### Problèmes courants

#### Erreur de connexion Firebase
```
Error: Firebase configuration invalid
```
**Solution** : Vérifiez vos clés dans le fichier `.env`

#### Erreur de permissions
```
Error: Insufficient permissions
```
**Solution** : Vérifiez que l'utilisateur a le bon rôle dans Firestore

#### Erreur de build
```
Error: Module not found
```
**Solution** : 
```bash
rm -rf node_modules package-lock.json
npm install
```

### Logs et debugging

#### Activer les logs détaillés
```bash
# Mode développement avec logs
VITE_DEBUG=true npm run dev
```

#### Vérifier la configuration Firebase
```bash
firebase projects:list
firebase use --add
```

## 📊 Monitoring

### Métriques à surveiller
- Nombre d'utilisateurs connectés
- Utilisation des APIs IA
- Erreurs d'authentification
- Performance des requêtes

### Outils recommandés
- **Firebase Analytics** : Métriques d'usage
- **Firebase Performance** : Performance de l'app
- **Sentry** : Monitoring des erreurs (à configurer)

## 🔄 Mise à jour

### Mise à jour des dépendances
```bash
npm update
npm audit fix
```

### Mise à jour de l'application
```bash
git pull origin main
npm install
npm run build
npm run deploy
```

## 📞 Support

### Ressources
- **Documentation Firebase** : https://firebase.google.com/docs
- **Documentation React** : https://react.dev
- **Documentation Material-UI** : https://mui.com

### Contact
Pour toute question technique :
1. Vérifiez la documentation
2. Consultez les issues GitHub
3. Contactez l'équipe de développement

---

**Note** : Cette documentation est mise à jour régulièrement. Consultez la version la plus récente sur le repository.
