// Constantes de l'application

// R<PERSON>les disponibles
export const ADMIN_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MODERATOR: 'moderator',
  CONTENT_MANAGER: 'content_manager',
} as const;

// Permissions disponibles
export const PERMISSIONS = {
  MANAGE_USERS: 'manage_users',
  MANAGE_SUBSCRIPTIONS: 'manage_subscriptions',
  MANAGE_SESSIONS: 'manage_sessions',
  MA<PERSON>GE_PRICING: 'manage_pricing',
  VIEW_ANALYTICS: 'view_analytics',
  MANAGE_CONTENT: 'manage_content',
  MANAGE_AI_APIS: 'manage_ai_apis',
  MANAGE_ACL: 'manage_acl',
} as const;

// Configuration des rôles et leurs permissions par défaut
export const ROLE_PERMISSIONS = {
  [ADMIN_ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),
  [ADMIN_ROLES.ADMIN]: [
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANA<PERSON>_SUBSCRIPTIONS,
    PERMISSIONS.MANAGE_SESSIONS,
    PERMISSIONS.MANAGE_PRICING,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.MANAGE_CONTENT,
    PERMISSIONS.MANAGE_AI_APIS,
  ],
  [ADMIN_ROLES.MODERATOR]: [
    PERMISSIONS.MANAGE_CONTENT,
    PERMISSIONS.VIEW_ANALYTICS,
  ],
  [ADMIN_ROLES.CONTENT_MANAGER]: [
    PERMISSIONS.MANAGE_SESSIONS,
    PERMISSIONS.MANAGE_CONTENT,
    PERMISSIONS.MANAGE_AI_APIS,
  ],
};

// Types de sessions disponibles
export const SESSION_TYPES = {
  MEDITATION: 'meditation',
  STORY: 'story',
  HYPNOSIS: 'hypnosis',
  PNL: 'pnl',
  RELAXATION: 'relaxation',
  BREATHING: 'breathing',
  CUSTOM: 'custom',
} as const;

// Catégories de sessions
export const SESSION_CATEGORIES = {
  SLEEP: 'sleep',
  STRESS: 'stress',
  FOCUS: 'focus',
  ENERGY: 'energy',
  HEALING: 'healing',
  PERSONAL_DEVELOPMENT: 'personal_development',
} as const;

// Langues supportées
export const SUPPORTED_LANGUAGES = {
  FR: 'fr',
  EN: 'en',
  ES: 'es',
  DE: 'de',
  IT: 'it',
} as const;

// Devises supportées
export const SUPPORTED_CURRENCIES = {
  EUR: 'EUR',
  USD: 'USD',
  GBP: 'GBP',
  CAD: 'CAD',
} as const;

// Durées d'abonnement
export const SUBSCRIPTION_DURATIONS = {
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
} as const;

// Fournisseurs IA
export const AI_PROVIDERS = {
  GROQ: 'groq',
  MISTRAL: 'mistral',
  GOOGLE: 'google',
  CHUTES: 'chutes',
} as const;

// Modèles IA par défaut
export const AI_MODELS = {
  [AI_PROVIDERS.GROQ]: [
    'mixtral-8x7b-32768',
    'llama2-70b-4096',
    'gemma-7b-it',
  ],
  [AI_PROVIDERS.MISTRAL]: [
    'mistral-tiny',
    'mistral-small',
    'mistral-medium',
    'mistral-large',
  ],
  [AI_PROVIDERS.GOOGLE]: [
    'gemini-pro',
    'gemini-pro-vision',
  ],
  [AI_PROVIDERS.CHUTES]: [
    'chutes-default',
    'chutes-wellness',
  ],
};

// Statuts des abonnements
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
  PENDING: 'pending',
} as const;

// Limites de l'application
export const APP_LIMITS = {
  MAX_SESSION_DURATION: 120, // minutes
  MIN_SESSION_DURATION: 1, // minutes
  MAX_SCRIPT_LINES: 1000,
  MAX_FEATURES_PER_PLAN: 20,
  MAX_AI_TOKENS: 4000,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
} as const;

// Messages d'erreur communs
export const ERROR_MESSAGES = {
  UNAUTHORIZED: 'Accès non autorisé',
  FORBIDDEN: 'Permission insuffisante',
  NOT_FOUND: 'Ressource non trouvée',
  VALIDATION_ERROR: 'Erreur de validation',
  NETWORK_ERROR: 'Erreur de connexion',
  UNKNOWN_ERROR: 'Une erreur inattendue s\'est produite',
} as const;

// Messages de succès communs
export const SUCCESS_MESSAGES = {
  CREATED: 'Créé avec succès',
  UPDATED: 'Mis à jour avec succès',
  DELETED: 'Supprimé avec succès',
  SAVED: 'Sauvegardé avec succès',
} as const;

// Configuration des notifications
export const NOTIFICATION_CONFIG = {
  DURATION: {
    SUCCESS: 3000,
    ERROR: 5000,
    WARNING: 4000,
    INFO: 3000,
  },
  POSITION: 'top-right' as const,
};
