// scripts/build-sessions.js
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Les scripts JSON sources sont DÉJÀ dans public
const SESSIONS_DATA_PUBLIC_DIR = path.join(__dirname, '../public/assets/sessionScripts');
const MANIFESTS_PUBLIC_DIR = path.join(__dirname, '../public/assets/manifests');

const languages = ['fr', 'en', 'es']; // Langues supportées

function buildSessionManifests() {
  fs.ensureDirSync(MANIFESTS_PUBLIC_DIR); // Crée le dossier s'il n'existe pas

  languages.forEach(lang => {
    const langDataDir = path.join(SESSIONS_DATA_PUBLIC_DIR, lang); // Chemin vers les JSON de la langue
    const manifestEntries = [];

    if (fs.existsSync(langDataDir)) {
      const files = fs.readdirSync(langDataDir);
      files.forEach(file => {
        if (path.extname(file) === '.json' && file !== 'manifest.json') { // Ignorer un éventuel ancien manifest.json dans ce dossier
          const filePath = path.join(langDataDir, file);
          try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const sessionData = JSON.parse(content);

            // Extraire les métadonnées pour le manifeste
            manifestEntries.push({
              id: sessionData.id, // Assurez-vous que vos JSON ont un champ 'id' qui est le nom du fichier sans .json
              title: sessionData.title,
              type: sessionData.type,
              estimatedDuration: sessionData.estimatedDuration || sessionData.durationMinutes || 0,
              tags: sessionData.tags ? sessionData.tags.slice(0, 3) : [],
              imageUrl: sessionData.imageUrl || null,
              isPremium: sessionData.isPremium || false,
              // Ajoutez d'autres métadonnées légères si nécessaire pour la liste
            });

            console.log(`Added ${lang}/${file} to manifest`);
          } catch (error) {
            console.error(`Error processing file ${lang}/${file}:`, error);
          }
        }
      });
    } else {
      console.warn(`Data directory not found for language: ${langDataDir}`);
    }

    const manifestOutputPath = path.join(MANIFESTS_PUBLIC_DIR, `manifest_${lang}.json`);
    fs.writeFileSync(manifestOutputPath, JSON.stringify({ sessions: manifestEntries }, null, 2));
    console.log(`Manifest for ${lang} written to ${manifestOutputPath}`);
  });
}

buildSessionManifests();