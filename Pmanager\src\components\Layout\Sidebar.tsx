import React from 'react';
import {
  Drawer,
  <PERSON>,
  <PERSON>Item,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Box,
} from '@mui/material';
import {
  Dashboard,
  People,
  AttachMoney,
  PlayCircle,
  Psychology,
  Analytics,
  Settings,
  Security,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const drawerWidth = 240;

interface MenuItem {
  text: string;
  icon: React.ReactElement;
  path: string;
  permission?: string;
}

const menuItems: MenuItem[] = [
  {
    text: 'Dashboard',
    icon: <Dashboard />,
    path: '/',
  },
  {
    text: 'Utilisateurs',
    icon: <People />,
    path: '/users',
    permission: 'manage_users',
  },
  {
    text: 'Tarification',
    icon: <AttachMoney />,
    path: '/pricing',
    permission: 'manage_pricing',
  },
  {
    text: 'Sessions',
    icon: <PlayCircle />,
    path: '/sessions',
    permission: 'manage_sessions',
  },
  {
    text: 'IA & Scripts',
    icon: <Psychology />,
    path: '/ai',
    permission: 'manage_ai_apis',
  },
  {
    text: 'Analytics',
    icon: <Analytics />,
    path: '/analytics',
    permission: 'view_analytics',
  },
  {
    text: 'ACL',
    icon: <Security />,
    path: '/acl',
    permission: 'manage_acl',
  },
  {
    text: 'Paramètres',
    icon: <Settings />,
    path: '/settings',
  },
];

interface SidebarProps {
  mobileOpen: boolean;
  onMobileClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ mobileOpen, onMobileClose }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission, adminUser } = useAuth();

  const handleNavigation = (path: string) => {
    navigate(path);
    onMobileClose();
  };

  const filteredMenuItems = menuItems.filter(item => 
    !item.permission || hasPermission(item.permission)
  );

  const drawer = (
    <Box>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          Piknowkyo Admin
        </Typography>
      </Toolbar>
      <Divider />
      
      {adminUser && (
        <Box sx={{ p: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Connecté en tant que
          </Typography>
          <Typography variant="body1" fontWeight="bold">
            {adminUser.displayName || adminUser.email}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {adminUser.role}
          </Typography>
        </Box>
      )}
      
      <Divider />
      
      <List>
        {filteredMenuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => handleNavigation(item.path)}
            >
              <ListItemIcon>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Box
      component="nav"
      sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
    >
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={onMobileClose}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
        }}
      >
        {drawer}
      </Drawer>
      
      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
        }}
        open
      >
        {drawer}
      </Drawer>
    </Box>
  );
};

export default Sidebar;
