import { useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { accessControlService, AccessResult, FeatureAccess } from '../services/accessControlService';
import { Session } from '../models';

export interface UseAccessControlReturn {
  // Vérifications d'accès
  checkSessionAccess: (session: Session) => AccessResult;
  checkAudioFeatureAccess: (feature: 'binaural' | 'ambient' | 'premium') => AccessResult;
  checkGameAccess: (gameId: string) => AccessResult;
  
  // Filtrage de contenu
  filterSessionsByAccess: (sessions: Session[]) => Session[];
  getLockedPremiumSessions: (sessions: Session[]) => Session[];
  
  // État d'accès
  featureAccess: FeatureAccess;
  canWatchAds: boolean;
  
  // Utilitaires
  getUpgradeMessage: (feature: string) => string;
  requiresAuth: (feature: string) => boolean;
  
  // Raccour<PERSON> pour les vérifications courantes
  canAccessPremiumSessions: boolean;
  canAccessBinauralAudio: boolean;
  canAccessAmbientAudio: boolean;
  canAccessGames: boolean;
  canAccessOfflineMode: boolean;
  hasNoAds: boolean;
}

/**
 * Hook personnalisé pour gérer le contrôle d'accès basé sur l'abonnement
 */
export const useAccessControl = (): UseAccessControlReturn => {
  const { userProfile, isAuthenticated } = useAuth();

  // Calculer l'accès aux fonctionnalités
  const featureAccess = useMemo(() => {
    return accessControlService.getFeatureAccess(userProfile);
  }, [userProfile]);

  // Fonctions de vérification d'accès
  const checkSessionAccess = useMemo(() => {
    return (session: Session) => accessControlService.checkSessionAccess(session, userProfile);
  }, [userProfile]);

  const checkAudioFeatureAccess = useMemo(() => {
    return (feature: 'binaural' | 'ambient' | 'premium') => 
      accessControlService.checkAudioFeatureAccess(feature, userProfile);
  }, [userProfile]);

  const checkGameAccess = useMemo(() => {
    return (gameId: string) => accessControlService.checkGameAccess(gameId, userProfile);
  }, [userProfile]);

  // Fonctions de filtrage
  const filterSessionsByAccess = useMemo(() => {
    return (sessions: Session[]) => accessControlService.filterSessionsByAccess(sessions, userProfile);
  }, [userProfile]);

  const getLockedPremiumSessions = useMemo(() => {
    return (sessions: Session[]) => accessControlService.getLockedPremiumSessions(sessions, userProfile);
  }, [userProfile]);

  // Utilitaires
  const canWatchAds = useMemo(() => {
    return accessControlService.canWatchAdsForUnlock(userProfile);
  }, [userProfile]);

  const getUpgradeMessage = useMemo(() => {
    return (feature: string) => accessControlService.getUpgradeMessage(feature, userProfile);
  }, [userProfile]);

  const requiresAuth = useMemo(() => {
    return (feature: string) => accessControlService.requiresAuthentication(feature);
  }, []);

  // Raccourcis pour les vérifications courantes
  const canAccessPremiumSessions = useMemo(() => {
    return featureAccess.sessions.premium;
  }, [featureAccess]);

  const canAccessBinauralAudio = useMemo(() => {
    return featureAccess.audio.binaural;
  }, [featureAccess]);

  const canAccessAmbientAudio = useMemo(() => {
    return featureAccess.audio.ambient;
  }, [featureAccess]);

  const canAccessGames = useMemo(() => {
    return featureAccess.sessions.premium; // Les jeux nécessitent premium
  }, [featureAccess]);

  const canAccessOfflineMode = useMemo(() => {
    return featureAccess.features.offline;
  }, [featureAccess]);

  const hasNoAds = useMemo(() => {
    return featureAccess.features.noAds;
  }, [featureAccess]);

  return {
    // Vérifications d'accès
    checkSessionAccess,
    checkAudioFeatureAccess,
    checkGameAccess,
    
    // Filtrage de contenu
    filterSessionsByAccess,
    getLockedPremiumSessions,
    
    // État d'accès
    featureAccess,
    canWatchAds,
    
    // Utilitaires
    getUpgradeMessage,
    requiresAuth,
    
    // Raccourcis
    canAccessPremiumSessions,
    canAccessBinauralAudio,
    canAccessAmbientAudio,
    canAccessGames,
    canAccessOfflineMode,
    hasNoAds
  };
};

/**
 * Hook pour vérifier l'accès à une session spécifique
 */
export const useSessionAccess = (session: Session | null) => {
  const { checkSessionAccess } = useAccessControl();
  
  return useMemo(() => {
    if (!session) return { allowed: false, reason: 'Session non trouvée' };
    return checkSessionAccess(session);
  }, [session, checkSessionAccess]);
};

/**
 * Hook pour vérifier l'accès à une fonctionnalité audio
 */
export const useAudioFeatureAccess = (feature: 'binaural' | 'ambient' | 'premium') => {
  const { checkAudioFeatureAccess } = useAccessControl();
  
  return useMemo(() => {
    return checkAudioFeatureAccess(feature);
  }, [feature, checkAudioFeatureAccess]);
};

/**
 * Hook pour vérifier l'accès aux jeux
 */
export const useGameAccess = (gameId: string) => {
  const { checkGameAccess } = useAccessControl();
  
  return useMemo(() => {
    return checkGameAccess(gameId);
  }, [gameId, checkGameAccess]);
};

/**
 * Hook pour filtrer les sessions selon l'accès utilisateur
 */
export const useFilteredSessions = (sessions: Session[]) => {
  const { filterSessionsByAccess, getLockedPremiumSessions } = useAccessControl();
  
  return useMemo(() => {
    const accessibleSessions = filterSessionsByAccess(sessions);
    const lockedSessions = getLockedPremiumSessions(sessions);
    
    return {
      accessibleSessions,
      lockedSessions,
      totalSessions: sessions.length,
      accessibleCount: accessibleSessions.length,
      lockedCount: lockedSessions.length
    };
  }, [sessions, filterSessionsByAccess, getLockedPremiumSessions]);
};
