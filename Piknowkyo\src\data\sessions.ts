// src/data/sessions.ts
import { Session, SessionManifestEntry } from '../models';
import { Language } from '../LangProvider'; // Assurez-vous que ce chemin est correct
import { scriptSyncService } from '../services/scriptSyncService';

// Fonction pour normaliser les données
function normalizeSessionData(sessionData: any): Session {
  const normalized: Partial<Session> = { ...sessionData }; // Commencer avec une copie partielle

  if (normalized.benefits && typeof normalized.benefits === 'string') {
    // Typage explicite de 'b' ici pour s'assurer que la fonction map travaille sur des chaînes
    normalized.benefits = (normalized.benefits as string).split(',').map((b: string) => b.trim());
  } else if (!Array.isArray(normalized.benefits)) {
    normalized.benefits = [];
  }

  if (normalized.durationMinutes && !normalized.estimatedDuration) {
    normalized.estimatedDuration = normalized.durationMinutes;
  }
  // S'assurer que estimatedDuration est un nombre, ou 0 si non défini ou NaN
  if (typeof normalized.estimatedDuration !== 'number' || isNaN(normalized.estimatedDuration)) {
    normalized.estimatedDuration = 0;
  }
  // Optionnel: supprimer durationMinutes s'il n'est plus nécessaire après avoir peuplé estimatedDuration
  // delete normalized.durationMinutes;

  if (!Array.isArray(normalized.tags)) {
    normalized.tags = [];
  }
  
  // S'assurer que les champs obligatoires de Session sont présents, même avec des valeurs par défaut
  // et que leur type correspond.
  normalized.id = String(normalized.id ?? 'unknown'); // Utiliser ?? pour gérer null et undefined
  normalized.title = String(normalized.title ?? 'Sans titre');
  normalized.description = String(normalized.description ?? '');
  normalized.type = String(normalized.type ?? 'custom');
  normalized.language = String(normalized.language ?? 'fr') as Language; // Assumer que la langue dans JSON est valide

  // Pour les champs optionnels qui doivent être d'un certain type s'ils existent
  if (normalized.audio && typeof normalized.audio !== 'object') normalized.audio = undefined;
  if (normalized.script && !Array.isArray(normalized.script)) normalized.script = undefined;
  if (normalized.comments && !Array.isArray(normalized.comments)) normalized.comments = undefined;


  return normalized as Session; // Caster vers Session après s'être assuré que les champs sont là
}

// Charger les métadonnées des sessions depuis Firestore en priorité, puis fallback vers les fichiers locaux
export async function fetchSessionManifest(lang: Language): Promise<SessionManifestEntry[]> {
  try {
    // Essayer d'abord de charger depuis Firestore
    console.log(`🔥 Tentative de chargement du manifeste depuis Firestore pour ${lang}`);
    const firestoreManifest = await scriptSyncService.getManifestFromFirestore(lang);

    if (firestoreManifest && firestoreManifest.length > 0) {
      console.log(`✅ Manifeste chargé depuis Firestore: ${firestoreManifest.length} sessions pour ${lang}`);
      return firestoreManifest;
    }

    console.log(`⚠️ Aucune session trouvée dans Firestore pour ${lang}, fallback vers les fichiers locaux`);
  } catch (error) {
    console.warn(`⚠️ Erreur lors du chargement depuis Firestore pour ${lang}, fallback vers les fichiers locaux:`, error);
  }

  // Fallback vers les fichiers locaux
  const manifestPath = `/assets/manifests/manifest_${lang}.json`;
  try {
    const response = await fetch(manifestPath);
    if (!response.ok) {
      if (response.status === 404) {
        console.warn(`Manifest not found for lang ${lang} at ${manifestPath}. Returning empty list.`);
        return [];
      }
      throw new Error(`Failed to load manifest for lang ${lang}: ${response.statusText} (path: ${manifestPath})`);
    }
    const manifestData: { sessions: SessionManifestEntry[] } = await response.json();
    // S'assurer que chaque entrée du manifeste a au moins les champs requis et les bons types
    const localManifest = (manifestData.sessions || []).map(entry => ({
        id: String(entry.id ?? 'unknown_manifest_id'),
        title: String(entry.title ?? 'Sans Titre (Manifeste)'),
        type: String(entry.type ?? 'custom'),
        estimatedDuration: Number(entry.estimatedDuration || 0),
        tags: Array.isArray(entry.tags) ? entry.tags.map(String) : [], // S'assurer que les tags sont des chaînes
        imageUrl: entry.imageUrl || undefined, // Garder undefined si null/undefined
        isPremium: Boolean(entry.isPremium || false),
    }));

    console.log(`📁 Manifeste chargé depuis les fichiers locaux: ${localManifest.length} sessions pour ${lang}`);
    return localManifest;
  } catch (error) {
    console.error(`Error fetching or parsing manifest from ${manifestPath}:`, error);
    return [];
  }
}

// Charger les détails complets d'UNE session depuis Firestore en priorité, puis fallback vers les fichiers locaux
export async function fetchSessionWithScript(sessionId: string, lang: Language): Promise<Session | null> {
  try {
    // Essayer d'abord de charger depuis Firestore
    console.log(`🔥 Tentative de chargement de la session ${sessionId} depuis Firestore pour ${lang}`);
    const firestoreSession = await scriptSyncService.getScriptFromFirestore(sessionId, lang);

    if (firestoreSession) {
      console.log(`✅ Session ${sessionId} chargée depuis Firestore pour ${lang}`);
      return firestoreSession;
    }

    console.log(`⚠️ Session ${sessionId} non trouvée dans Firestore pour ${lang}, fallback vers les fichiers locaux`);
  } catch (error) {
    console.warn(`⚠️ Erreur lors du chargement de ${sessionId} depuis Firestore pour ${lang}, fallback vers les fichiers locaux:`, error);
  }

  // Fallback vers les fichiers locaux
  const sessionFilePath = `/assets/sessionScripts/${lang}/${sessionId}.json`;
  try {
    const response = await fetch(sessionFilePath);
    if (!response.ok) {
      // Si le fichier n'est pas trouvé, retourner null silencieusement.
      // La page de détail gérera l'affichage "Session non trouvée".
      console.log(`📁 Session ${sessionId} non trouvée dans les fichiers locaux pour ${lang}`);
      return null;
    }
    const rawSessionData: any = await response.json();
    const localSession = normalizeSessionData(rawSessionData);
    console.log(`📁 Session ${sessionId} chargée depuis les fichiers locaux pour ${lang}`);
    return localSession;
  } catch (error) {
    console.error(`Error fetching or parsing session details for ${sessionId} (lang ${lang}):`, error);
    return null;
  }
}