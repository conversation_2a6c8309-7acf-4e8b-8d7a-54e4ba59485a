# Piknowkyo Admin - Gestionnaire d'Administration

Interface d'administration pour l'application Piknowkyo, construite avec React + TypeScript et Material-UI.

## 🚀 Fonctionnalités

### ✅ Implémentées
- **Authentification** : Connexion avec email/mot de passe et Google Auth
- **Dashboard** : Vue d'ensemble avec statistiques et métriques
- **Gestion des Prix** : CRUD complet pour les plans d'abonnement
- **Système ACL** : Gestion des rôles et permissions
- **Interface moderne** : Design responsive avec Material-UI

### 🔄 En cours de développement
- **Gestion des Sessions** : Création et modification des sessions de méditation
- **Gestion des Utilisateurs** : Administration des comptes utilisateurs
- **IA & Scripts** : Intégration avec Groq, Mistral, Google AI, Chutes.ai
- **Analytics** : Tableaux de bord détaillés
- **Paramètres** : Configuration globale de l'application

## 🛠️ Technologies

- **Frontend** : React 19.1.0 + TypeScript
- **UI** : Material-UI (MUI) 6.1.9
- **Routing** : React Router DOM 7.6.0
- **State Management** : Redux Toolkit 2.8.2
- **Forms** : React Hook Form + Yup validation
- **Backend** : Firebase (Auth, Firestore, Storage)
- **Build Tool** : Vite 6.3.5
- **Notifications** : React Hot Toast

## 📦 Installation

1. **Cloner le repository**
```bash
cd Pmanager
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration Firebase**
```bash
cp .env.example .env
# Éditer .env avec vos clés Firebase
```

4. **Lancer en développement**
```bash
npm run dev
```

L'application sera disponible sur `http://localhost:3001`

## 🔧 Configuration

### Firebase
Le projet utilise la même configuration Firebase que l'application principale Piknowkyo. Assurez-vous que les collections suivantes existent :

- `admin_users` : Utilisateurs administrateurs
- `subscription_pricing` : Plans de tarification
- `session_templates` : Modèles de sessions
- `ai_providers` : Configuration des fournisseurs IA
- `user_subscriptions` : Abonnements des utilisateurs

### Rôles et Permissions

#### Rôles disponibles :
- **super_admin** : Accès complet à toutes les fonctionnalités
- **admin** : Accès à la plupart des fonctionnalités
- **moderator** : Accès limité à la modération de contenu
- **content_manager** : Gestion du contenu uniquement

#### Permissions :
- `manage_users` : Gestion des utilisateurs
- `manage_subscriptions` : Gestion des abonnements
- `manage_sessions` : Gestion des sessions
- `manage_pricing` : Gestion des prix
- `view_analytics` : Consultation des analytics
- `manage_content` : Gestion du contenu
- `manage_ai_apis` : Gestion des APIs IA
- `manage_acl` : Gestion des accès

## 🔐 Sécurité

### Authentification
- Connexion sécurisée via Firebase Auth
- Support Google OAuth
- Vérification des permissions côté client et serveur

### ACL (Access Control List)
- Système de rôles hiérarchiques
- Permissions granulaires
- Protection des routes sensibles

## 🎨 Interface

### Design System
- Material Design 3 via MUI
- Thème cohérent avec l'application principale
- Interface responsive (mobile-first)
- Mode sombre/clair (à venir)

### Navigation
- Sidebar avec navigation principale
- Header avec profil utilisateur
- Breadcrumbs pour la navigation (à venir)

## 🤖 Intégration IA

### Fournisseurs supportés
- **Groq** : Génération rapide de scripts
- **Mistral** : IA française pour contenus localisés
- **Google AI** : Gemini pour génération avancée
- **Chutes.ai** : Spécialisé dans le bien-être

### Fonctionnalités IA
- Génération automatique de scripts de méditation
- Personnalisation par type de session
- Gestion des coûts et quotas
- Monitoring de l'utilisation

## 📊 Analytics

### Métriques suivies
- Nombre d'utilisateurs (total, actifs, premium)
- Sessions jouées et durée moyenne
- Revenus (mensuel, annuel, croissance)
- Utilisation de l'IA (requêtes, tokens, coûts)

## 🚀 Déploiement

### Build de production
```bash
npm run build
```

### Déploiement Firebase
```bash
npm run deploy
```

## 🧪 Tests

```bash
# Tests unitaires
npm run test

# Linting
npm run lint
```

## 📝 Structure du projet

```
src/
├── components/          # Composants réutilisables
│   └── Layout/         # Composants de mise en page
├── contexts/           # Contextes React
├── pages/              # Pages de l'application
├── services/           # Services (API, Firebase)
├── types/              # Types TypeScript
├── config/             # Configuration
└── hooks/              # Hooks personnalisés (à venir)
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence privée - voir le fichier LICENSE pour plus de détails.

## 🆘 Support

Pour toute question ou problème :
- Créer une issue sur GitHub
- Contacter l'équipe de développement

---

**Note** : Ce gestionnaire d'administration est en cours de développement actif. Certaines fonctionnalités peuvent être incomplètes ou en cours d'implémentation.
