import React, { useState } from 'react';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  Divider,
  CircularProgress,
} from '@mui/material';
import { Google } from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import FirstAdminSetup from '@/components/FirstAdminSetup';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showFirstAdminSetup, setShowFirstAdminSetup] = useState(false);

  const { signIn, signInWithGoogle } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await signIn(email, password);
      navigate('/');
    } catch (error: any) {
      const errorMessage = error.message || 'Erreur de connexion';
      setError(errorMessage);

      // Si l'erreur indique qu'aucun admin n'existe, proposer de créer le premier
      if (errorMessage.includes('non autorisé') || errorMessage.includes('Email ou mot de passe incorrect')) {
        // Ajouter un bouton pour créer le premier admin après quelques tentatives
        setTimeout(() => {
          if (confirm('Aucun administrateur trouvé. Voulez-vous créer le premier compte administrateur ?')) {
            setShowFirstAdminSetup(true);
          }
        }, 1000);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError('');
    setLoading(true);

    try {
      await signInWithGoogle();
      navigate('/');
    } catch (error: any) {
      setError(error.message || 'Erreur de connexion avec Google');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography component="h1" variant="h4" gutterBottom>
              Piknowkyo Admin
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Connectez-vous pour accéder à l'administration
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Adresse email"
              name="email"
              autoComplete="email"
              autoFocus
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Mot de passe"
              type="password"
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Se connecter'}
            </Button>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                ou
              </Typography>
            </Divider>

            <Button
              fullWidth
              variant="outlined"
              startIcon={<Google />}
              onClick={handleGoogleSignIn}
              disabled={loading}
              sx={{ mb: 2 }}
            >
              Continuer avec Google
            </Button>
          </Box>

          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Accès réservé aux administrateurs autorisés
            </Typography>

            <Button
              variant="text"
              size="small"
              onClick={() => setShowFirstAdminSetup(true)}
              sx={{ mt: 1 }}
            >
              Créer le premier administrateur
            </Button>
          </Box>
        </Paper>
      </Box>

      <FirstAdminSetup
        open={showFirstAdminSetup}
        onClose={() => setShowFirstAdminSetup(false)}
        onSuccess={() => {
          setError('');
          // Optionnel: pré-remplir les champs avec les identifiants créés
          setEmail('<EMAIL>');
          setPassword('AdminPiknowkyo2024!');
        }}
      />
    </Container>
  );
};

export default Login;
