{"root": ["./src/App.test.tsx", "./src/App.tsx", "./src/GlobalStyles.tsx", "./src/LangProvider.tsx", "./src/ThemeProvider.tsx", "./src/firebase.ts", "./src/i18n.ts", "./src/index.tsx", "./src/models.ts", "./src/react-app-env.d.ts", "./src/reportWebVitals.ts", "./src/setupTests.ts", "./src/styled.d.ts", "./src/themes.ts", "./src/vite-env.d.ts", "./src/components/AudioConfigPanel.tsx", "./src/components/BottomBar.tsx", "./src/components/JournalEntryForm.tsx", "./src/components/LanguageSwitcher.tsx", "./src/components/MainMenu.tsx", "./src/components/MonetizationModal.tsx", "./src/components/NetworkStatusNotifier.tsx", "./src/components/NotificationTest.tsx", "./src/components/Preferences.tsx", "./src/components/Questionnaire.tsx", "./src/components/ReduxExample.tsx", "./src/components/ReusableModal.tsx", "./src/components/SessionsList.tsx", "./src/components/SplashScreen.tsx", "./src/components/SyncStatusIndicator.tsx", "./src/components/Toast.tsx", "./src/components/UnlockTimer.tsx", "./src/components/common/Button.tsx", "./src/components/common/Card.tsx", "./src/components/common/index.ts", "./src/components/ui/Button.tsx", "./src/components/ui/Card.tsx", "./src/components/ui/index.ts", "./src/data/audioAssets.ts", "./src/data/sessions.ts", "./src/games/common/gameUtils.ts", "./src/games/common/models.tsx", "./src/games/common/components/GameModal.tsx", "./src/games/common/components/GameTimer.tsx", "./src/games/common/components/ScoreDisplay.tsx", "./src/games/zen-tetris/GameComponent.tsx", "./src/games/zen-tetris/logic.ts", "./src/games/zen-tetris/styles.ts", "./src/games/zen-tetris/components/OrientationHint.tsx", "./src/games/zen-tetris/hooks/useOrientation.ts", "./src/games/zen-tetris/utils/haptics.ts", "./src/models/script.d.ts", "./src/models/script.model.ts", "./src/pages/AboutPage.tsx", "./src/pages/AudioAssetsConfigPage.tsx", "./src/pages/BlogPage.tsx", "./src/pages/BlogPostCommentsPage.tsx", "./src/pages/CategoriesPage.tsx", "./src/pages/GamesPage.tsx", "./src/pages/HistoryPage.tsx", "./src/pages/HomePage.tsx", "./src/pages/JournalPage.tsx", "./src/pages/LeaderboardPage.tsx", "./src/pages/MonetizationPage.tsx", "./src/pages/NotFoundPage.tsx", "./src/pages/PlayerPage.tsx", "./src/pages/ProfilePage.tsx", "./src/pages/QuizPage.tsx", "./src/pages/SessionDetailPage.tsx", "./src/pages/SessionsPage.tsx", "./src/pages/SettingsPage.tsx", "./src/pages/StatsPage.tsx", "./src/services/firebase.ts", "./src/services/monetizationService.ts", "./src/services/piperVoices.ts", "./src/services/scriptsService.ts", "./src/services/syncService.ts", "./src/services/tts.ts", "./src/services/ttsVoices.ts", "./src/services/useNetworkStatus.ts", "./src/services/usePushNotifications.ts", "./src/services/piper/audio.ts", "./src/services/piper/config.ts", "./src/services/piper/diagnostics.ts", "./src/services/piper/fixtures.ts", "./src/services/piper/http.ts", "./src/services/piper/index.ts", "./src/services/piper/inference.ts", "./src/services/piper/onnx-loader.ts", "./src/services/piper/opfs.ts", "./src/services/piper/piper.js", "./src/services/piper/test-import.ts", "./src/services/piper/test.ts", "./src/services/piper/text-tester.ts", "./src/services/piper/types.ts", "./src/services/piper/voice-discovery.ts", "./src/store/hooks.ts", "./src/store/index.ts", "./src/store/slices/audioAssetsSlice.ts", "./src/store/slices/authSlice.ts", "./src/store/slices/journalSlice.ts", "./src/store/slices/monetizationSlice.ts", "./src/store/slices/networkSlice.ts", "./src/store/slices/sessionsSlice.ts", "./src/store/slices/syncSlice.ts", "./src/testJoff/testFirebase.tsx"], "errors": true, "version": "5.8.3"}