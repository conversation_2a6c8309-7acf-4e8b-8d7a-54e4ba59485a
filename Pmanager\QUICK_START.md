# 🚀 Démarrage Rapide - Piknowkyo Admin

## ⚡ Installation Express

```bash
# 1. Aller dans le dossier Pmanager
cd Pmanager

# 2. Installer les dépendances
npm install

# 3. Copier la configuration
cp .env.example .env

# 4. Lancer l'application
npm run dev
```

## 🔧 Configuration Firebase

### Option 1: Utiliser la configuration existante (Recommandé)
La configuration Firebase est déjà incluse dans le code. L'application utilisera automatiquement le projet `piknowkyo-777`.

### Option 2: Utiliser votre propre projet Firebase
1. Créer un nouveau projet Firebase
2. Activer Authentication (Email/Password + Google)
3. Créer une base Firestore
4. Modifier `src/config/firebase.ts` avec vos clés

## 👤 Créer le Premier Administrateur

### Méthode 1: Via l'interface (Plus simple)
1. Ouvrir `http://localhost:3001`
2. <PERSON><PERSON><PERSON> sur "Créer le premier administrateur"
3. <PERSON><PERSON><PERSON><PERSON> le formulaire
4. Se connecter avec les identifiants créés

### Méthode 2: Via le script
```bash
node scripts/init-admin.js
```

**Identifiants par défaut :**
- Email: `<EMAIL>`
- Mot de passe: `AdminPiknowkyo2024!`

## 🎯 Accès à l'Application

1. **URL**: `http://localhost:3001`
2. **Se connecter** avec les identifiants administrateur
3. **Explorer** les fonctionnalités :
   - Dashboard avec métriques
   - Gestion des prix d'abonnement
   - Configuration (à venir)

## 🔍 Résolution des Problèmes

### Erreur "Email ou mot de passe incorrect"
- ✅ Cliquer sur "Créer le premier administrateur"
- ✅ Ou exécuter `node scripts/init-admin.js`

### Erreur de configuration Firebase
- ✅ Vérifier que Firestore est activé
- ✅ Vérifier que Authentication est configuré
- ✅ Déployer les règles : `firebase deploy --only firestore:rules`

### Erreur de dépendances
```bash
rm -rf node_modules package-lock.json
npm install
```

## 📱 Fonctionnalités Disponibles

### ✅ Implémentées
- **Authentification** sécurisée
- **Dashboard** avec analytics
- **Gestion des prix** (CRUD complet)
- **Système ACL** avec permissions

### 🔄 En développement
- Gestion des sessions
- Gestion des utilisateurs
- Configuration des APIs IA
- Analytics avancées

## 🛡️ Sécurité

- **Règles Firestore** : Accès restreint aux admins
- **Permissions** : Contrôle granulaire par rôle
- **Validation** : Côté client et serveur

## 📞 Support

- **Documentation** : Voir `README.md` et `INSTALLATION.md`
- **Problèmes** : Vérifier les logs de la console
- **Firebase** : Console Firebase pour debug

---

**🎊 Votre gestionnaire d'administration est prêt !**
