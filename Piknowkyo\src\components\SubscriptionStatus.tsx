import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiClock, FiStar, FiGift, FiArrowRight } from 'react-icons/fi';
import { FaCrown } from 'react-icons/fa';
import { useAuth } from '../contexts/AuthContext';
import ReusableModal from './ReusableModal';

interface SubscriptionStatusProps {
  showUpgradeButton?: boolean;
  compact?: boolean;
}

const StatusContainer = styled.div<{ $compact?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: ${({ $compact }) => $compact ? '0.75rem' : '1rem'};
  padding: ${({ $compact }) => $compact ? '1rem' : '1.5rem'};
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  border: 1px solid ${({ theme }) => theme.border || '#e1e5e9'};
`;

const StatusHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
`;

const StatusInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const StatusIcon = styled.div<{ $type: 'free' | 'premium' | 'trial' }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: ${({ $type }) => {
    switch ($type) {
      case 'premium': return 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)';
      case 'trial': return 'linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%)';
      default: return 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)';
    }
  }};
  color: white;
  font-size: 1.25rem;
`;

const StatusText = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const StatusTitle = styled.h3`
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: ${({ theme }) => theme.text};
`;

const StatusSubtitle = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.textSecondary || '#6b7280'};
`;

const TrialProgress = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background: ${({ theme }) => theme.surfaceAlt || '#f3f4f6'};
  border-radius: 3px;
  overflow: hidden;
`;

const ProgressFill = styled.div<{ $percentage: number }>`
  height: 100%;
  width: ${({ $percentage }) => $percentage}%;
  background: linear-gradient(90deg, #9b59b6 0%, #e74c3c 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: ${({ theme }) => theme.textSecondary || '#6b7280'};
`;

const UpgradeButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
`;

const FeaturesList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 1rem 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const FeatureItem = styled.li`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.text};

  &::before {
    content: '✓';
    color: #10b981;
    font-weight: bold;
  }
`;

const UpgradeModal = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  text-align: center;
`;

const PremiumFeatures = styled.div`
  display: grid;
  gap: 1rem;
  margin: 1rem 0;
`;

const FeatureCard = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: ${({ theme }) => theme.surfaceAlt || '#f8fafc'};
  border-radius: 8px;
  text-align: left;
`;

const FeatureIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 0.875rem;
`;

const PriceTag = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 0;
`;

const SubscriptionStatus: React.FC<SubscriptionStatusProps> = ({ 
  showUpgradeButton = true, 
  compact = false 
}) => {
  const { t } = useTranslation();
  const { userProfile, hasActivePremium, getRemainingTrialDays } = useAuth();
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  if (!userProfile) return null;

  const { plan, status, isTrialActive } = userProfile.subscription;
  const remainingDays = getRemainingTrialDays();
  const trialProgress = isTrialActive ? ((14 - remainingDays) / 14) * 100 : 0;

  const getStatusIcon = () => {
    if (isTrialActive) return <FiGift />;
    if (plan === 'premium') return <FaCrown />;
    return <FiStar />;
  };

  const getStatusType = (): 'free' | 'premium' | 'trial' => {
    if (isTrialActive) return 'trial';
    return plan;
  };

  const getStatusTitle = () => {
    if (isTrialActive) {
      return t('subscription.trialActive', 'Essai Premium');
    }
    return t(`subscription.${plan}`, plan === 'premium' ? 'Premium' : 'Gratuit');
  };

  const getStatusSubtitle = () => {
    if (isTrialActive) {
      return t('subscription.trialRemaining', `${remainingDays} jours restants`, { days: remainingDays });
    }
    if (plan === 'premium') {
      return t('subscription.premiumActive', 'Accès complet débloqué');
    }
    return t('subscription.freeActive', 'Accès limité');
  };

  const handleUpgrade = () => {
    setShowUpgradeModal(true);
  };

  const premiumFeatures = [
    {
      icon: <FiStar />,
      title: t('features.unlimitedSessions', 'Sessions illimitées'),
      description: t('features.unlimitedSessionsDesc', 'Accès à toutes les sessions premium')
    },
    {
      icon: <FaCrown />,
      title: t('features.advancedAudio', 'Audio avancé'),
      description: t('features.advancedAudioDesc', 'Sons binauraux et ambiants exclusifs')
    },
    {
      icon: <FiGift />,
      title: t('features.noAds', 'Sans publicité'),
      description: t('features.noAdsDesc', 'Expérience sans interruption')
    }
  ];

  return (
    <>
      <StatusContainer $compact={compact}>
        <StatusHeader>
          <StatusInfo>
            <StatusIcon $type={getStatusType()}>
              {getStatusIcon()}
            </StatusIcon>
            <StatusText>
              <StatusTitle>{getStatusTitle()}</StatusTitle>
              <StatusSubtitle>{getStatusSubtitle()}</StatusSubtitle>
            </StatusText>
          </StatusInfo>
          
          {showUpgradeButton && !hasActivePremium() && (
            <UpgradeButton onClick={handleUpgrade}>
              <FiArrowRight />
              {t('subscription.upgrade', 'Passer à Premium')}
            </UpgradeButton>
          )}
        </StatusHeader>

        {isTrialActive && (
          <TrialProgress>
            <ProgressBar>
              <ProgressFill $percentage={trialProgress} />
            </ProgressBar>
            <ProgressText>
              <span>{t('subscription.trialProgress', 'Progression de l\'essai')}</span>
              <span>{Math.round(trialProgress)}%</span>
            </ProgressText>
          </TrialProgress>
        )}

        {!compact && plan === 'free' && !isTrialActive && (
          <FeaturesList>
            <FeatureItem>{t('features.basicSessions', 'Sessions de base')}</FeatureItem>
            <FeatureItem>{t('features.limitedAudio', 'Audio de base')}</FeatureItem>
            <FeatureItem>{t('features.withAds', 'Avec publicités')}</FeatureItem>
          </FeaturesList>
        )}
      </StatusContainer>

      <ReusableModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        title={t('subscription.upgradeToPremium', 'Passer à Premium')}
        maxWidth="500px"
      >
        <UpgradeModal>
          <div>
            <h3>{t('subscription.unlockPremium', 'Débloquez toutes les fonctionnalités premium')}</h3>
            <p>{t('subscription.upgradeDescription', 'Profitez d\'une expérience complète sans limites')}</p>
          </div>

          <PriceTag>
            <FaCrown />
            <span>9,99€ / {t('subscription.month', 'mois')}</span>
          </PriceTag>

          <PremiumFeatures>
            {premiumFeatures.map((feature, index) => (
              <FeatureCard key={index}>
                <FeatureIcon>{feature.icon}</FeatureIcon>
                <div>
                  <h4 style={{ margin: '0 0 0.25rem 0', fontSize: '0.875rem', fontWeight: '600' }}>
                    {feature.title}
                  </h4>
                  <p style={{ margin: 0, fontSize: '0.75rem', opacity: 0.8 }}>
                    {feature.description}
                  </p>
                </div>
              </FeatureCard>
            ))}
          </PremiumFeatures>

          <UpgradeButton onClick={() => {
            // TODO: Implémenter la logique de paiement
            console.log('Redirection vers le paiement...');
          }}>
            <FaCrown />
            {t('subscription.startPremium', 'Commencer Premium')}
          </UpgradeButton>

          <p style={{ fontSize: '0.75rem', opacity: 0.7, margin: 0 }}>
            {t('subscription.cancelAnytime', 'Annulable à tout moment')}
          </p>
        </UpgradeModal>
      </ReusableModal>
    </>
  );
};

export default SubscriptionStatus;
