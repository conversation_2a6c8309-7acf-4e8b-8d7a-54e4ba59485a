import React, { useState, useEffect } from 'react';
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  People,
  AttachMoney,
  PlayCircle,
  TrendingUp,
} from '@mui/icons-material';
import { Analytics } from '@/types';

// Composant pour les cartes de statistiques
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, subtitle }) => (
  <Card>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="overline">
            {title}
          </Typography>
          <Typography variant="h4" component="div">
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="textSecondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        <Box
          sx={{
            backgroundColor: color,
            borderRadius: '50%',
            p: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {React.cloneElement(icon, { sx: { color: 'white' } })}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const Dashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        // Simulation des données - remplacer par un vrai service
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockAnalytics: Analytics = {
          users: {
            total: 1250,
            active: 890,
            premium: 156,
            newThisMonth: 89,
          },
          sessions: {
            totalPlayed: 5420,
            averageDuration: 18.5,
            mostPopular: ['Méditation du matin', 'Relaxation profonde', 'Sommeil réparateur'],
          },
          revenue: {
            monthly: 2340,
            yearly: 28080,
            growth: 12.5,
          },
          ai: {
            requestsThisMonth: 234,
            tokensUsed: 45600,
            costEstimate: 23.45,
          },
        };
        
        setAnalytics(mockAnalytics);
      } catch (error) {
        setError('Erreur lors du chargement des données');
        console.error('Erreur analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!analytics) {
    return (
      <Alert severity="warning" sx={{ mt: 2 }}>
        Aucune donnée disponible
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        {/* Statistiques principales */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Utilisateurs Total"
            value={analytics.users.total.toLocaleString()}
            icon={<People />}
            color="#1976d2"
            subtitle={`${analytics.users.newThisMonth} nouveaux ce mois`}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Utilisateurs Actifs"
            value={analytics.users.active.toLocaleString()}
            icon={<People />}
            color="#2e7d32"
            subtitle={`${Math.round((analytics.users.active / analytics.users.total) * 100)}% du total`}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Abonnés Premium"
            value={analytics.users.premium.toLocaleString()}
            icon={<AttachMoney />}
            color="#ed6c02"
            subtitle={`${Math.round((analytics.users.premium / analytics.users.total) * 100)}% du total`}
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Sessions Jouées"
            value={analytics.sessions.totalPlayed.toLocaleString()}
            icon={<PlayCircle />}
            color="#9c27b0"
            subtitle={`${analytics.sessions.averageDuration} min en moyenne`}
          />
        </Grid>

        {/* Revenus */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Revenus
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {analytics.revenue.monthly.toLocaleString()}€
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Ce mois
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {analytics.revenue.yearly.toLocaleString()}€
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Cette année
                  </Typography>
                </Box>
              </Grid>
            </Grid>
            <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <TrendingUp color="success" sx={{ mr: 1 }} />
              <Typography variant="body2" color="success.main">
                +{analytics.revenue.growth}% par rapport au mois dernier
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Sessions populaires */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Sessions les plus populaires
            </Typography>
            {analytics.sessions.mostPopular.map((session, index) => (
              <Box key={index} sx={{ mb: 1 }}>
                <Typography variant="body1">
                  {index + 1}. {session}
                </Typography>
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Utilisation IA */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Utilisation de l'IA
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="primary">
                    {analytics.ai.requestsThisMonth}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Requêtes ce mois
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="primary">
                    {analytics.ai.tokensUsed.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Tokens utilisés
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" color="primary">
                    {analytics.ai.costEstimate.toFixed(2)}€
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Coût estimé
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
